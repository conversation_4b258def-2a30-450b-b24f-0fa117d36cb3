// Test CockroachDB Connection Script
// Tests connectivity to the new CockroachDB instance

const { Pool } = require('pg');

// CockroachDB connection string with actual password
const COCKROACH_DB_URL = "postgresql://shans:<EMAIL>:26257/defaultdb?sslmode=verify-full";

console.log('⚠️  IMPORTANT: Please replace <ENTER-SQL-USER-PASSWORD> with the actual password in the connection string');
console.log('Current connection string:', COCKROACH_DB_URL);

async function testCockroachConnection() {
  // Check if password is still placeholder
  if (COCKROACH_DB_URL.includes('<ENTER-SQL-USER-PASSWORD>')) {
    console.log('❌ Please update the connection string with the actual password');
    console.log('Edit this file and replace <ENTER-SQL-USER-PASSWORD> with your actual CockroachDB password');
    return;
  }

  const pool = new Pool({
    connectionString: COCKROACH_DB_URL,
    ssl: { rejectUnauthorized: false }
  });

  let client;
  
  try {
    console.log('🔗 Testing CockroachDB connection...');
    
    client = await pool.connect();
    
    // Test basic connectivity
    const result = await client.query('SELECT version()');
    console.log('✅ Successfully connected to CockroachDB');
    console.log('Database version:', result.rows[0].version);
    
    // Test database creation capabilities
    const dbResult = await client.query('SELECT current_database()');
    console.log('Current database:', dbResult.rows[0].current_database);
    
    // Check if we can create tables (test permissions)
    await client.query(`
      CREATE TABLE IF NOT EXISTS test_connection (
        id SERIAL PRIMARY KEY,
        test_field VARCHAR(50)
      )
    `);
    
    await client.query('DROP TABLE IF EXISTS test_connection');
    
    console.log('✅ Database permissions verified - can create/drop tables');
    
  } catch (error) {
    console.error('❌ Error connecting to CockroachDB:', error.message);
    
    if (error.message.includes('password authentication failed')) {
      console.log('💡 Tip: Check your password and username');
    } else if (error.message.includes('connection refused')) {
      console.log('💡 Tip: Check your connection string and network access');
    } else if (error.message.includes('SSL')) {
      console.log('💡 Tip: Check SSL configuration');
    }
    
    throw error;
  } finally {
    if (client) {
      client.release();
    }
    await pool.end();
  }
}

// Run the test
testCockroachConnection().catch(console.error);
