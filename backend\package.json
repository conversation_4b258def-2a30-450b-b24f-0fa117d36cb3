{"name": "shans-backend", "version": "1.0.0", "description": "Backend server for Shans System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "npm install --production=false", "postinstall": "echo 'Skipping Playwright install for deployment'", "migrate": "node run-migrations.js", "migrate-categories": "node migrate-categories.js", "create-categories": "node create-categories-table.js", "create-car-brands": "node create-car-brands-table.js", "create-car-models": "node create-car-models-table.js"}, "dependencies": {"bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "canvas": "^3.1.0", "cloudinary": "^2.6.1", "cors": "^2.8.5", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.21.1", "express-rate-limit": "^7.4.1", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.4", "node-fetch": "^3.3.2", "nodemailer": "^6.9.16", "openai": "^5.5.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.3.31", "pdfkit": "^0.15.1", "pg": "^8.13.1", "playwright": "^1.50.1", "readable-stream": "^3.6.2", "string_decoder": "^1.3.0", "util-deprecate": "^1.0.2", "wkhtmltopdf": "^0.4.0", "xlsx": "^0.18.5"}, "puppeteer": {"browsers": {"chrome": "stable"}}, "engines": {"node": "18.x"}, "author": "", "license": "ISC"}