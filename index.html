<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <!-- Add performance-focused meta tags -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="theme-color" content="#ffffff">
  <meta name="format-detection" content="telephone=no">
  <title>Shans System</title>
  <script src="auth-utils.js"></script>
  <style>
    /* Add hardware acceleration for animations and transitions */
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
      backface-visibility: hidden;
      transform: translateZ(0);
      will-change: transform;
    }

    /* Optimize input elements for touch */
    input, select, textarea {
      -webkit-appearance: none;
      appearance: none;
      user-select: text;
      -webkit-user-select: text;
    }

    /* Optimize animations */
    @keyframes spin {
      0% { transform: rotate(0deg) translateZ(0); }
      100% { transform: rotate(360deg) translateZ(0); }
    }

    /* Enhanced loading screen - positioned at top for better visibility */
    #loadingScreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.98);
      z-index: 99999; /* Higher z-index to ensure it's on top */
      display: flex;
      flex-direction: column;
      justify-content: flex-start; /* Changed from center to flex-start */
      align-items: center;
      padding-top: 80px; /* Add top padding to position content at top */
      transform: translateZ(0);
      will-change: transform;
      backdrop-filter: blur(2px); /* Add subtle blur effect */
      -webkit-backdrop-filter: blur(2px);
    }

    #loadingScreen .spinner {
      border: 10px solid #f3f3f3;
      border-top: 10px solid #3498db;
      border-radius: 50%;
      width: 80px; /* Larger spinner for better visibility */
      height: 80px;
      animation: spin 1s linear infinite;
      margin-bottom: 25px;
      transform: translateZ(0);
      will-change: transform;
      box-shadow: 0 4px 8px rgba(0,0,0,0.1); /* Add shadow for depth */
    }

    #loadingScreen p {
      color: #333;
      font-size: 18px; /* Larger text */
      font-weight: 600; /* Bold text */
      margin: 0;
      text-align: center;
      text-shadow: 0 1px 2px rgba(0,0,0,0.1); /* Subtle text shadow */
    }

    /* Enhanced error message styling */
    #loadingScreen .error-message {
      color: #dc3545;
      margin-top: 15px;
      text-align: center;
      max-width: 80%;
      font-size: 16px;
      font-weight: 500;
      background-color: rgba(220, 53, 69, 0.1);
      padding: 12px 20px;
      border-radius: 8px;
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    /* Enhanced retry button styling */
    #loadingScreen .retry-button {
      margin-top: 20px;
      padding: 12px 24px;
      background-color: #4a90e2;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 600;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      transition: all 0.3s ease;
    }

    #loadingScreen .retry-button:hover {
      background-color: #357abd;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    /* Mobile responsive adjustments for loading screen */
    @media (max-width: 768px) {
      #loadingScreen {
        padding-top: 60px; /* Reduce top padding on mobile */
      }

      #loadingScreen .spinner {
        width: 60px; /* Slightly smaller spinner on mobile */
        height: 60px;
        border-width: 8px;
      }

      #loadingScreen p {
        font-size: 16px; /* Slightly smaller text on mobile */
        padding: 0 20px; /* Add horizontal padding for better readability */
      }

      #loadingScreen .error-message {
        font-size: 14px;
        max-width: 90%; /* Use more screen width on mobile */
      }
    }

    /* Optimize table rendering */
    .div-table {
      transform: translateZ(0);
      will-change: transform;
      contain: content;
    }

    .div-table-row {
      contain: content;
    }

    /* Optimize toast animations */
    @keyframes fadeInOut {
      0% { opacity: 0; transform: translateZ(0); }
      10% { opacity: 0.95; transform: translateZ(0); }
      90% { opacity: 0.95; transform: translateZ(0); }
      100% { opacity: 0; transform: translateZ(0); }
    }

    .toast {
      transform: translateZ(0);
      will-change: transform, opacity;
    }

    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0; /* Changed to 0 to accommodate loading screen */
      background-color: #f4f4f4;
    }
    .container {
      max-width: 800px;
      margin: auto;
      background: white;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
      color: #333;
    }
    .section {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 5px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input, select, textarea {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }

    /* Fix checkbox styling - override the width: 100% rule */
    input[type="checkbox"] {
      width: 18px;
      height: 18px;
      padding: 0;
      margin: 0;
      margin-right: 10px;
      vertical-align: middle;
      cursor: pointer;
      appearance: auto; /* Ensure native checkbox appearance */
      -webkit-appearance: checkbox; /* For Safari */
      -moz-appearance: checkbox; /* For Firefox */
    }

    /* Checkbox container styling */
    .checkbox-container {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
    }

    .checkbox-container label {
      margin: 0;
      cursor: pointer;
      user-select: none; /* Prevent text selection when clicking */
    }
    textarea {
      resize: vertical;
      min-height: 80px;
    }
    input[readonly] {
      background-color: #e9e9e9;
      cursor: not-allowed;
    }
    button {
      background-color: #4a90e2;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
    }
    button:hover {
      background-color: #81b0e6;
    }
    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      table-layout: auto;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
      word-wrap: break-word;
    }
    th {
      background-color: #f2f2f2;
    }
    #productList {
      list-style-type: none;
      padding: 0;
      margin-top: 10px;
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    #productList li {
      padding: 8px;
      cursor: pointer;
      border-bottom: 1px solid #eee;
      display: flex;
      flex-direction: column;
    }
    #productList li:hover {
      background-color: #f0f0f0;
    }
    #productList li .product-name {
      font-weight: bold;
    }
    #productList li .product-details {
      font-size: 0.9em;
      color: #666;
      margin-top: 3px;
    }
    .loader {
      border: 4px solid #f3f3f3; /* Light grey */
      border-top: 4px solid #3498db; /* Blue */
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      display: none;
      margin-left: 10px;
      vertical-align: middle;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .table-responsive {
      width: 100%;
      overflow-x: auto;
    }

    /* Div-based Table Styling */
    .table-container {
      width: 100%;
      margin-bottom: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
      overflow-x: auto; /* Allow horizontal scrolling on desktop */
    }

    .div-table {
      display: flex;
      flex-direction: column;
      width: 100%;
      background-color: white;
      font-size: 14px;
      border: 1px solid #e0e0e0;
      margin-bottom: 0;
    }

    .div-table-row {
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #e0e0e0;
    }

    .div-table-row:nth-child(even) {
      background-color: #f9f9f9;
    }

    .div-table-row:hover {
      background-color: #f0f7ff;
    }

    .div-table-header {
      background-color: #f2f2f2;
      font-weight: bold;
      border-bottom: 2px solid #d0d0d0;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .div-table-cell {
      padding: 8px 10px;
      border-right: 1px solid #e0e0e0;
      white-space: nowrap; /* No wrapping on desktop */
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
    }

    .div-table-cell:last-child {
      border-right: none;
    }

    .div-table-heading {
      color: #333;
      font-weight: 700; /* Consistent bold weight */
    }

    /* Column-specific styling */
    .product-column {
      flex: 4;
      min-width: 250px;
      max-width: 350px;
      justify-content: flex-start;
      padding-left: 12px;
    }

    .qty-column {
      flex: 0.3;
      min-width: 30px;
      max-width: 40px;
      justify-content: center;
      font-weight: 700; /* Make it bold */
      text-align: center;
      font-size: 13px;
    }

    .price-column {
      flex: 0.7;
      min-width: 70px;
      justify-content: flex-end;
      padding-right: 12px;
      font-size: 13px;
    }

    /* For the tax column that may be hidden */
    .tax-column {
      display: flex; /* Will be toggled with JavaScript */
    }

    /* Div Table Body */
    .div-table-body {
      display: flex;
      flex-direction: column;
    }

    .new-product-label {
      color: #ff9900;
      font-weight: bold;
      margin-left: 5px;
      font-size: 12px;
    }

    /* Stock indicator styles */
    .stock-available {
      color: #28a745;
      font-weight: bold;
    }

    .stock-empty {
      color: #dc3545;
      font-weight: bold;
    }

    /* Action column styling */
    .action-column {
      flex: 0 0 30px; /* Fixed small width for action column */
      min-width: 30px;
      max-width: 30px;
      justify-content: center;
      text-align: center;
    }

    /* Remove X styling */
    .remove-x {
      color: #ff3b30;
      font-weight: bold;
      font-size: 16px;
      cursor: pointer;
      padding: 2px 4px; /* Reduced padding */
      transition: transform 0.2s ease, color 0.2s ease;
      display: inline-block;
    }

    .remove-x:hover {
      transform: scale(1.2);
      color: #cc0000;
    }

    /* Unit Cost Edit Functionality */
    .unit-cost-container {
      position: relative;
      display: flex;
      align-items: center;
    }

    .unit-cost-disabled {
      background-color: #f5f5f5;
      color: #999;
      cursor: not-allowed;
    }

    .edit-icon {
      margin-left: 8px;
      cursor: pointer;
      font-size: 16px;
      padding: 4px;
      border-radius: 3px;
      transition: background-color 0.2s ease;
    }

    .edit-icon:hover {
      background-color: #e0e0e0;
    }

    /* Responsive Design */
    @media (max-width: 830px) {
      body {
        padding: 10px;
      }

      .container {
        padding: 15px;
      }

      /* Adjust table for medium screens */
      .div-table-cell {
        padding: 6px 8px;
        font-size: 13px;
      }

      /* Adjust column widths for medium screens */
      .product-column {
        min-width: 180px;
        flex: 3;
      }

      .qty-column {
        min-width: 25px;
        flex: 0.3;
        font-size: 12px;
      }

      .price-column {
        min-width: 60px;
        flex: 0.6;
        font-size: 12px;
      }
    }

    /* Specific adjustments for small screens */
    @media (max-width: 650px) {
      body {
        padding: 5px;
      }

      .container {
        padding: 12px;
      }

      /* Header button styling for mobile */
      .container > div:first-child {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 15px;
      }

      .container > div:first-child > div {
        display: flex !important;
        flex-direction: column;
        gap: 10px;
        width: 100%;
      }

      /* Admin button mobile styling */
      .admin-nav-button {
        width: 100% !important;
        padding: 12px 0 !important;
        text-align: center;
        margin-right: 0 !important;
        font-size: 16px !important;
        box-sizing: border-box;
      }

      /* Only apply full width to main navigation and form buttons, not all buttons */
      .admin-nav-button,
      .button-group button {
        width: 100%;
        padding: 12px 0;
        font-size: 16px;
        margin-right: 0;
      }
      
      /* Keep normal button behavior for other buttons like Add Product */
      button:not(.admin-nav-button):not(.button-group button) {
        width: auto;
        padding: 10px 15px;
        font-size: 16px;
        margin-right: 10px;
      }

      .loader {
        width: 25px;
        height: 25px;
      }

      .section {
        padding: 10px;
      }

      .button-group {
        flex-direction: column;
        align-items: stretch;
      }

      /* Adjust table for small screens */
      .table-container {
        overflow-x: visible; /* Disable horizontal scrolling on mobile */
        box-shadow: none;
        border-radius: 0;
      }

      .div-table {
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }

      .div-table-cell {
        padding: 3px 4px; /* Reduced padding to save space */
        font-size: 12px;
        white-space: normal; /* Allow text wrapping */
        word-wrap: break-word; /* Break long words */
      }

      /* Make all table headers smaller on mobile */
      .div-table-heading {
        font-size: 9px !important; /* Smaller header font size */
      }

      /* Fixed column widths for mobile - optimized to fit all columns */
      /* Base widths for 7 columns (without tax) */
      .product-column {
        flex: 0 0 22%; /* Further reduced to 22% */
        width: 22%;
        max-width: 22%;
        min-width: 22%;
        justify-content: flex-start;
        text-align: left;
      }
      
      /* Adjust product column when tax is enabled (8 columns total) */
      .div-table:has(.tax-column) .product-column {
        flex: 0 0 18%; /* Even smaller when tax column is present */
        width: 18%;
        max-width: 18%;
        min-width: 18%;
      }

      .qty-column {
        flex: 0 0 7%; /* Reduced QTY column to 7% */
        width: 7%;
        max-width: 7%;
        min-width: 7%;
        font-size: 10px;
        text-align: center;
        justify-content: center;
      }

      .price-column {
        flex: 0 0 11%; /* Reduced to 11% to save space */
        width: 11%;
        max-width: 11%;
        min-width: 11%;
        font-size: 9px;
        text-align: right;
        justify-content: flex-end;
      }

      /* Special handling for tax column when present */
      .price-column.tax-column {
        flex: 0 0 9%; /* Reduced tax column width */
        width: 9%;
        max-width: 9%;
        min-width: 9%;
      }

      .action-column {
        flex: 0 0 7%; /* Increased to 7% for better visibility */
        width: 7%;
        max-width: 7%;
        min-width: 7%;
        text-align: center;
        justify-content: center;
      }
    }

    /* Specific adjustments for very small screens */
    @media (max-width: 480px) {
      body {
        padding: 0;
      }

      .container {
        padding: 10px;
      }

      /* Ensure header buttons remain properly styled on very small screens */
      .admin-nav-button {
        font-size: 14px !important;
        padding: 10px 0 !important;
      }

      /* Only apply full width to main navigation and form buttons */
      .admin-nav-button,
      .button-group button {
        font-size: 14px;
        padding: 10px 0;
      }
      
      /* Keep normal button behavior for other buttons */
      button:not(.admin-nav-button):not(.button-group button) {
        font-size: 14px;
        padding: 8px 12px;
      }

      /* Mobile-optimized table */
      .div-table-cell {
        padding: 4px 5px;
        font-size: 11px;
        white-space: normal; /* Allow text wrapping on mobile */
        word-wrap: break-word; /* Break long words */
      }

      /* Make all table headers consistent size on mobile */
      .div-table-heading {
        font-size: 8px !important; /* Reduced header font size to save space */
      }

      /* Fixed column sizing for very small mobile - consistent widths */
      .div-table-row {
        display: flex;
        width: 100%;
      }

      /* Maintain the same fixed widths as 650px breakpoint */
      /* Base widths for 7 columns (without tax) */
      .product-column {
        flex: 0 0 22%; /* Further reduced to 22% */
        width: 22%;
        max-width: 22%;
        min-width: 22%;
        justify-content: flex-start;
        text-align: left;
      }
      
      /* Adjust product column when tax is enabled (8 columns total) */
      .div-table:has(.tax-column) .product-column {
        flex: 0 0 18%; /* Even smaller when tax column is present */
        width: 18%;
        max-width: 18%;
        min-width: 18%;
      }

      .qty-column {
        flex: 0 0 7%; /* Reduced QTY column to 7% */
        width: 7%;
        max-width: 7%;
        min-width: 7%;
        font-size: 9px;
        text-align: center;
        justify-content: center;
      }

      .price-column {
        flex: 0 0 11%; /* Reduced to 11% to save space */
        width: 11%;
        max-width: 11%;
        min-width: 11%;
        font-size: 9px;
        text-align: right;
        justify-content: flex-end;
      }

      /* Special handling for tax column when present */
      .price-column.tax-column {
        flex: 0 0 9%; /* Reduced tax column width */
        width: 9%;
        max-width: 9%;
        min-width: 9%;
      }

      .action-column {
        flex: 0 0 7%; /* Increased to 7% for better visibility */
        width: 7%;
        max-width: 7%;
        min-width: 7%;
        text-align: center;
        justify-content: center;
      }

      .remove-x {
        font-size: 14px; /* Slightly smaller X on mobile */
        padding: 1px 2px;
      }
    }
    .hidden {
      display: none;
    }
    .remove-button {
      background-color: #ff4d4d;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    .remove-button:hover {
      background-color: #e60000;
    }



    /* Toast Container and Toast Styles */
    .toast-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000; /* on top of loading screen */
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .toast {
      background-color: #333;
      color: #fff;
      padding: 10px 15px;
      border-radius: 5px;
      opacity: 0.95;
      font-size: 14px;
      animation: fadeInOut 3s forwards;
    }
    .toast.success {
      background-color: #28a745;
    }
    .toast.error {
      background-color: #dc3545;
    }
    .toast.info {
      background-color: #007bff;
    }
    @keyframes fadeInOut {
      0% { opacity: 0; }
      10% { opacity: 0.95; }
      90% { opacity: 0.95; }
      100% { opacity: 0; }
    }

    /* Admin navigation button styles */
    .admin-nav-button {
      background-color: #9b59b6;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      display: inline-block;
      transition: background-color 0.3s ease;
      margin-right: 10px;
    }

    .admin-nav-button:hover {
      background-color: #8e44ad;
      color: white;
    }

    .admin-nav-button.hidden {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loadingScreen">
    <div class="spinner"></div>
    <p>Loading products, please wait...</p>
    <div class="error-message" style="display: none;"></div>
    <button class="retry-button" style="display: none;" onclick="retryInitialization()">Retry</button>
  </div>

  <!-- Toast Container (for all messages) -->
  <div class="toast-container" id="toastContainer"></div>

  <div class="container">


    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h1 style="margin: 0;">Receipt and Quotation Generator</h1>
      <div style="display: flex; align-items: center; gap: 10px;">
        <a href="convert-to-receipt.html" class="admin-nav-button" style="background-color: #FF9800;">Convert to Receipt</a>
        <a href="Admin/index.html" id="adminDashboardBtn" class="admin-nav-button hidden">Admin Dashboard</a>
        <button onclick="logout()" style="background-color: #e74c3c; padding: 8px 16px; font-size: 14px;">Logout</button>
      </div>
    </div>

    <!-- Company Selection Section -->
    <div class="section">
      <h2>Select Company</h2>
      <label for="companySelect">Choose a company:</label>
      <select id="companySelect" required>
        <option value="" disabled selected>Select a company</option>
        <option value="company1">Shans Accessories PTY LTD</option>
        <option value="company2">Shans Autosport PTY LTD</option>
        <option value="company3">Shans Motorstyle PTY LTD</option>
      </select>
    </div>

    <!-- Customer Information Section -->
    <div class="section">
      <h2>Customer Information</h2>
      <label for="customerName">Name:</label>
      <input type="text" id="customerName">

      <label for="customerEmail">Email:</label>
      <input type="email" id="customerEmail">

      <label for="customerAddress">Address:</label>
      <input type="text" id="customerAddress">

      <label for="customerPhone">Phone:</label>
      <input type="tel" id="customerPhone">

      <label for="salespersonName">Salesperson Name:</label>
      <input type="text" id="salespersonName" placeholder="Enter your name...">
    </div>

    <!-- Shipping Information Section -->
    <div class="section">
      <h2>Shipping Information</h2>
      <div class="checkbox-container">
        <input type="checkbox" id="sameAsBilling" checked>
        <label for="sameAsBilling">Ship to the same address as billing</label>
      </div>

      <div id="shippingInfo" class="hidden">
        <label for="shippingName">Name:</label>
        <input type="text" id="shippingName">

        <label for="shippingEmail">Email:</label>
        <input type="email" id="shippingEmail">

        <label for="shippingAddress">Address:</label>
        <input type="text" id="shippingAddress">

        <label for="shippingPhone">Phone:</label>
        <input type="tel" id="shippingPhone">
      </div>
    </div>

    <!-- Add Products Section -->
    <div class="section">
      <h2>Add Products</h2>
      <label for="productSearch">Search Products:</label>
      <input type="text" id="productSearch" placeholder="Type to search products or enter new product">
      <div style="display: flex; align-items: center; margin-bottom: 10px;">
        <ul id="productList"></ul>
        <div class="loader" id="searchLoader"></div>
      </div>

      <label for="quantity">Quantity:</label>
      <div>
        <input type="number" id="quantity" value="1" min="1">
      </div>

      <label for="unitCost">Unit Cost (R):</label>
      <div class="unit-cost-container">
        <input type="number" id="unitCost" value="0" min="0" step="0.01" placeholder="Enter unit cost" readonly class="unit-cost-disabled">
        <span class="edit-icon" id="unitCostEditIcon" onclick="toggleUnitCostEdit()" title="Click to edit unit cost">✏️</span>
      </div>

      <label for="price">Selling Price (R):</label>
      <input type="number" id="price" value="0" min="0" step="0.01" placeholder="Enter selling price">

      <label for="room" class="hidden" id="roomLabel">Room:</label>
      <input type="text" id="room" class="hidden" placeholder="Select a product to see the room" readonly>

      <button onclick="addSelectedProduct()">Add Product</button>
    </div>

    <!-- Selected Products and Totals Section -->
    <div class="section">
      <h2>Selected Products</h2>
      <!-- Div-based Table (works on all screen sizes) -->
      <div class="table-container">
        <div class="div-table">
          <!-- Table Header - Will be populated by JavaScript -->
          <div class="div-table-row div-table-header" id="table-header">
            <!-- Header cells will be added dynamically -->
          </div>
          <!-- Table Body - Will be populated by JavaScript -->
          <div class="div-table-body" id="selectedProductsBody">
            <!-- Product rows will be added dynamically -->
          </div>
        </div>
      </div>

      <div class="checkbox-container">
        <input type="checkbox" id="Tax">
        <label for="Tax">Add Tax</label>
      </div>

      <!-- Display the calculated values -->
      <p>Product Amount (Subtotal): R<span id="subtotalAmount">0.00</span></p>
      <p>Tax (15%): R<span id="taxAmount">0.00</span></p>
      <p>Total Amount: R<span id="totalAmount">0.00</span></p>
      <p>Total Profit: R<span id="totalProfitAmount">0.00</span></p>

      <!-- Comments Section -->
      <div class="section" style="border:none; padding:0;">
        <label for="paymentMethod">Payment Method:</label>
        <select id="paymentMethod">
          <option value="Cash">Cash</option>
          <option value="EFT">EFT</option>
        </select>
      </div>

      <!-- Added Comments Box -->
      <div class="section" style="border:none; padding:0;">
        <label for="comments">Additional Comments:</label>
        <textarea id="comments" placeholder="Enter any additional comments here..."></textarea>
      </div>

      <div class="button-group">
        <button onclick="generateReceipt()">Generate Receipt</button>
        <button onclick="generateQuotation()">Generate Quotation</button>
        <button onclick="generateInvoice()">Generate Invoice</button>
      </div>
    </div>
  </div>

  <script>
    // Performance optimization variables
    const DEBOUNCE_DELAY = 300;
    const PRODUCTS_PER_PAGE = 20;
    let currentPage = 1;
    let isLoading = false;
    let hasMoreProducts = true;
    let searchTimeout;
    let selectedProducts = [];
    let allProducts = [];
    let newProductCounter = 1;
    let isSelectingProduct = false;

    // API_BASE_URL is now defined in auth-utils.js
    const CURRENCY_SYMBOL = 'R';
    const TAX_RATE = 0.15;

    // Cache DOM elements
    const DOM = {
      productSearchInput: document.getElementById('productSearch'),
      productList: document.getElementById('productList'),
      searchLoader: document.getElementById('searchLoader'),
      roomInput: document.getElementById('room'),
      roomLabel: document.getElementById('roomLabel'),
      sameAsBillingCheckbox: document.getElementById('sameAsBilling'),
      shippingInfoDiv: document.getElementById('shippingInfo'),
      paymentMethodSelect: document.getElementById('paymentMethod'),
      taxCheckbox: document.getElementById('Tax'),
      taxAmountSpan: document.getElementById('taxAmount'),
      subtotalAmountSpan: document.getElementById('subtotalAmount'),
      totalAmountSpan: document.getElementById('totalAmount'),
      totalProfitAmountSpan: document.getElementById('totalProfitAmount'),
      tableHeader: document.getElementById('table-header'),
      selectedProductsBody: document.getElementById('selectedProductsBody'),
      companySelect: document.getElementById('companySelect'),
      commentsTextarea: document.getElementById('comments'),
      loadingScreen: document.getElementById('loadingScreen'),
      priceInput: document.getElementById('price'),
      unitCostInput: document.getElementById('unitCost'),
      quantityInput: document.getElementById('quantity'),
      customerNameInput: document.getElementById('customerName'),
      customerEmailInput: document.getElementById('customerEmail'),
      customerAddressInput: document.getElementById('customerAddress'),
      customerPhoneInput: document.getElementById('customerPhone'),
      shippingNameInput: document.getElementById('shippingName'),
      shippingEmailInput: document.getElementById('shippingEmail'),
      shippingAddressInput: document.getElementById('shippingAddress'),
      shippingPhoneInput: document.getElementById('shippingPhone'),
      salespersonNameInput: document.getElementById('salespersonName')
    };

    // Storage object is now defined in auth-utils.js

    // Optimized debounce function
    function debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }

    // Optimized product fetching with pagination
    async function fetchAllProducts() {
      try {
        isLoading = true;
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        // Add cache busting parameter to prevent cached responses
        const cacheBuster = `&_t=${Date.now()}`;
        const response = await fetch(`${API_BASE_URL}/products?page=${currentPage}&limit=${PRODUCTS_PER_PAGE}${cacheBuster}`, {
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const newProducts = await response.json();



        if (newProducts.length < PRODUCTS_PER_PAGE) {
          hasMoreProducts = false;
        }

        // Filter out duplicates by checking item_code
        const existingItemCodes = new Set(allProducts.map(p => p.item_code));
        const uniqueNewProducts = newProducts.filter(product => !existingItemCodes.has(product.item_code));

        allProducts = [...allProducts, ...uniqueNewProducts];


        currentPage++;

        // Only update UI if user is actively searching
        const searchTerm = DOM.productSearchInput.value.toLowerCase().trim();
        if (searchTerm.length > 0) {
          displayFilteredProducts();
        }
      } catch (error) {
        console.error('Error fetching products:', error);
        if (error.name === 'AbortError') {
          showToastMessage('Request timed out. Please check your connection.', 'error');
        } else {
          showToastMessage('Failed to load products. Please try again.', 'error');
        }
        throw error; // Re-throw to be caught by the caller
      } finally {
        isLoading = false;
      }
    }

    // Optimized product search with debouncing
    const debouncedSearch = debounce(() => {
      DOM.searchLoader.style.display = 'inline-block';
      displayFilteredProducts();
      DOM.searchLoader.style.display = 'none';
    }, DEBOUNCE_DELAY);

    // Simple and reliable product display
    function displayFilteredProducts() {
      const searchTerm = DOM.productSearchInput.value.toLowerCase().trim();

      // Clear existing products
      DOM.productList.innerHTML = '';

      // Only show products if user has typed something (minimum 1 character)
      if (searchTerm.length === 0) {
        return; // Don't show any products when search is empty
      }

      const filteredProducts = allProducts.filter(product =>
        product.item_name && product.item_name.toLowerCase().includes(searchTerm)
      );

      if (filteredProducts.length === 0) {
        const li = document.createElement('li');
        li.textContent = allProducts.length === 0 ? 'Loading products...' : 'No products found';
        li.style.cursor = 'default';
        DOM.productList.appendChild(li);
        return;
      }

      // Create document fragment for better performance
      const fragment = document.createDocumentFragment();

      // Simple approach: show each filtered product directly
      filteredProducts.forEach((product, index) => {
        const li = document.createElement('li');

        const productNameDiv = document.createElement('div');
        productNameDiv.className = 'product-name';
        productNameDiv.textContent = product.item_name;

        const productDetailsDiv = document.createElement('div');
        productDetailsDiv.className = 'product-details';

        const stockClass = product.available_stock > 0 ? 'stock-available' : 'stock-empty';
        productDetailsDiv.innerHTML = `Room: <b>${product.room_name || 'N/A'}</b> | Stock: <span class="${stockClass}">${product.available_stock || 0}</span>`;

        li.appendChild(productNameDiv);
        li.appendChild(productDetailsDiv);

        // Add click event listener
        li.addEventListener('click', (e) => {
          e.preventDefault();
          e.stopPropagation();
          isSelectingProduct = true;
          selectProduct(product);
          isSelectingProduct = false;
        });

        fragment.appendChild(li);
      });

      DOM.productList.appendChild(fragment);

      // Load more products if needed
      if (hasMoreProducts && !isLoading && DOM.productList.scrollHeight <= DOM.productList.clientHeight + 100) {
        fetchAllProducts();
      }
    }

    // Optimized event listeners
    DOM.productSearchInput.addEventListener('input', debouncedSearch, { passive: true });

    // Clear dropdown when user clicks away or clears the search
    DOM.productSearchInput.addEventListener('blur', () => {
      // Delay to allow clicking on dropdown items
      setTimeout(() => {
        if (!isSelectingProduct) {
          DOM.productList.innerHTML = '';
        }
      }, 500);
    });

    // Clear dropdown when search is cleared
    DOM.productSearchInput.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        DOM.productSearchInput.value = '';
        DOM.productList.innerHTML = '';
      }
    });

    DOM.sameAsBillingCheckbox.addEventListener('change', toggleShippingInfo, { passive: true });
    DOM.taxCheckbox.addEventListener('change', () => {
      requestAnimationFrame(updateSelectedProductsTable);
    }, { passive: true });

    // Optimized table updates using requestAnimationFrame
    function updateSelectedProductsTable() {
      console.log('updateSelectedProductsTable function called');
      console.log('selectedProducts.length:', selectedProducts.length);
      requestAnimationFrame(() => {
        console.log('Inside requestAnimationFrame');
        console.log('DOM.selectedProductsBody element:', DOM.selectedProductsBody);
        DOM.selectedProductsBody.innerHTML = '';
        updateTableHeader();

        if (selectedProducts.length === 0) {
          console.log('No products in array - showing empty message');
          const emptyRow = document.createElement('div');
          emptyRow.className = 'div-table-row';
          emptyRow.style.justifyContent = 'center';
          emptyRow.style.padding = '20px';
          emptyRow.style.color = '#666';
          emptyRow.innerHTML = '<i>No products selected yet. Search and add products above.</i>';
          DOM.selectedProductsBody.appendChild(emptyRow);
          calculateAndDisplayTotal();
          return;
        }
        
        console.log('Products found - creating table rows');

        const fragment = document.createDocumentFragment();
        selectedProducts.forEach((product, index) => {
          const row = createProductRow(product, index);
          fragment.appendChild(row);
        });

        DOM.selectedProductsBody.appendChild(fragment);
        calculateAndDisplayTotal();

        // Adjust column widths for desktop only (mobile uses fixed widths)
        setTimeout(() => {
          adjustColumnWidthsForMobile();
        }, 50); // Small delay to ensure DOM is fully rendered
      });
    }

    // Optimized row creation
    function createProductRow(product, index) {
      const row = document.createElement('div');
      row.className = 'div-table-row';

      // Product name cell
      const nameCell = document.createElement('div');
      nameCell.className = 'div-table-cell product-column';

      // Create the product name with room information underneath
      let nameContent = product.name;

      // Add NEW label if it's a new product
      if (product.is_new) {
        nameContent += '<span class="new-product-label">(NEW)</span>';
      }

      // Add room information underneath if available and not 'N/A'
      if (product.room_name && product.room_name !== 'N/A') {
        const abbreviatedRoom = abbreviateRoomName(product.room_name);
        nameContent += `<br><span style="color: #0066cc; font-weight: bold; font-size: 0.85em;">${abbreviatedRoom}</span>`;
      }

      nameCell.innerHTML = nameContent;
      row.appendChild(nameCell);

      // Quantity cell
      const qtyCell = document.createElement('div');
      qtyCell.className = 'div-table-cell qty-column';
      qtyCell.textContent = product.quantity;
      qtyCell.style.fontWeight = '700';
      qtyCell.style.textAlign = 'center';
      row.appendChild(qtyCell);

      // Price cell
      const priceCell = document.createElement('div');
      priceCell.className = 'div-table-cell price-column';
      if (DOM.taxCheckbox.checked) {
        const netPrice = product.price * (1 - TAX_RATE);
        priceCell.textContent = `R ${Math.round(netPrice)}`;
      } else {
        priceCell.textContent = `R ${Math.round(product.price)}`;
      }
      row.appendChild(priceCell);

      // Unit Cost cell
      const costCell = document.createElement('div');
      costCell.className = 'div-table-cell price-column';
      costCell.textContent = `R ${Math.round(product.unit_cost || 0)}`;
      row.appendChild(costCell);

      // Profit cell (selling price - unit cost)
      const profitCell = document.createElement('div');
      profitCell.className = 'div-table-cell price-column';
      const profitPerUnit = product.price - (product.unit_cost || 0); // price is the selling price
      const totalProfit = profitPerUnit * product.quantity;
      profitCell.textContent = `R ${Math.round(totalProfit)}`; // Show only total profit for the line
      profitCell.style.color = profitPerUnit >= 0 ? '#28a745' : '#dc3545'; // Green for profit, red for loss
      row.appendChild(profitCell);

      if (DOM.taxCheckbox.checked) {
        // Tax cell
        const taxCell = document.createElement('div');
        taxCell.className = 'div-table-cell price-column tax-column';
        const taxPerUnit = product.price * TAX_RATE;
        taxCell.textContent = `R ${Math.round(taxPerUnit)}`;
        row.appendChild(taxCell);
      }

      // Total cell
      const totalCell = document.createElement('div');
      totalCell.className = 'div-table-cell price-column';
      const lineTotal = product.price * product.quantity;
      totalCell.textContent = `R ${Math.round(lineTotal)}`;
      row.appendChild(totalCell);

      // Action cell (remove button)
      const actionCell = document.createElement('div');
      actionCell.className = 'div-table-cell action-column';
      actionCell.innerHTML = `<span class="remove-x" onclick="removeProduct(${index})">✕</span>`;
      row.appendChild(actionCell);

      return row;
    }

    // Memory management
    function cleanup() {
      selectedProducts = [];
      allProducts = [];
      currentPage = 1;
      hasMoreProducts = true;
      DOM.productList.innerHTML = '';
      DOM.selectedProductsBody.innerHTML = '';
      updateSelectedProductsTable();
    }

    // Add cleanup on page unload
    window.addEventListener('unload', cleanup);

    // Add resize event listener for dynamic column adjustment
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        adjustColumnWidthsForMobile();
      }, 250); // Debounce resize events
    });

    // Add retry functionality
    function retryInitialization() {
      const loadingScreen = document.getElementById('loadingScreen');
      const errorMessage = loadingScreen.querySelector('.error-message');
      const retryButton = loadingScreen.querySelector('.retry-button');

      // Reset loading screen
      errorMessage.style.display = 'none';
      retryButton.style.display = 'none';
      loadingScreen.querySelector('p').textContent = 'Loading products, please wait...';

      // Clear any existing data
      allProducts = [];
      currentPage = 1;
      hasMoreProducts = true;

      // Restart initialization
      initializeApp();
    }

    // Separate initialization function
    async function initializeApp() {
      const loadingScreen = document.getElementById('loadingScreen');
      const errorMessage = loadingScreen.querySelector('.error-message');
      const retryButton = loadingScreen.querySelector('.retry-button');

      try {
        // Show loading screen
        loadingScreen.style.display = 'flex';

        const isAuthenticated = await initAuth({
          requireAuth: true,
          showLoading: true
        });
        if (!isAuthenticated) {
          return; // initAuth will handle the redirect
        }

        checkAndShowAdminButton();

        // Add timeout for loading
        const loadingTimeout = setTimeout(() => {
          errorMessage.textContent = 'Loading timeout. Please check your connection and try again.';
          errorMessage.style.display = 'block';
          retryButton.style.display = 'block';
          loadingScreen.querySelector('p').textContent = 'Connection Error';
        }, 10000);

        try {
          await fetchAllProducts();
        } catch (error) {
          console.error('Error loading products:', error);
          errorMessage.textContent = 'Failed to load products. Please try again.';
          errorMessage.style.display = 'block';
          retryButton.style.display = 'block';
          loadingScreen.querySelector('p').textContent = 'Error Loading Products';
        } finally {
          clearTimeout(loadingTimeout);
        }

        const selectedCompanyCookie = getCookie('selectedCompany');
        if (selectedCompanyCookie) {
          const selectedCompany = JSON.parse(selectedCompanyCookie);
          DOM.companySelect.value = selectedCompany.key;
        }

        loadExistingOrderData();
        updateSelectedProductsTable();

        // Hide loading screen on success
        loadingScreen.style.display = 'none';
      } catch (error) {
        console.error('Initialization error:', error);
        errorMessage.textContent = 'An error occurred while initializing the app. Please try again.';
        errorMessage.style.display = 'block';
        retryButton.style.display = 'block';
        loadingScreen.querySelector('p').textContent = 'Initialization Error';
      }
    }

    // Update window.onload to use the new initialization function
    window.onload = initializeApp;

    // Update checkAndShowAdminButton to use shared auth utilities
    function checkAndShowAdminButton() {
      if (isAdmin()) {
        const adminButton = document.getElementById('adminDashboardBtn');
        if (adminButton) {
          adminButton.classList.remove('hidden');
        }
      }
    }



    // Company Information
    const companies = {
      company1: {
        name: "Shans Accessories PTY LTD",
        bankingInformation: `
          First National Bank<br>
          Account :  ***********<br>
          Branch code 257705<br>
          Swift code FIRNZAJJ
        `
      },
      company2: {
        name: "Shans Autosport PTY LTD",
        bankingInformation: `
          Business Account<br>
          Capitec Current Account<br>
          Account: **********
        `
      },
      company3: {
        name: "Shans Motorstyle PTY LTD",
        bankingInformation: `
          SHANS MOTORSTYLE (PTY) LTD<br>
          Gold Business Account<br>
          Account Number: ***********<br>
          Branch Code: 250655<br>
          Swift Code: FIRNZAJJ
        `
      }
    };

    /**
     * Display a non-blocking toast message in the top-right corner.
     * @param {string} message - The message to display
     * @param {string} [type='info'] - 'success', 'error', or 'info'
     */
    function showToastMessage(message, type = 'info') {
      const toastContainer = document.getElementById('toastContainer');
      const toast = document.createElement('div');
      toast.classList.add('toast', type);
      toast.textContent = message;
      toastContainer.appendChild(toast);
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }

    function setCookie(name, value, days) {
      try {
        const d = new Date();
        d.setTime(d.getTime() + (days*24*60*60*1000));
        const expires = "expires="+ d.toUTCString();
        document.cookie = name + "=" + encodeURIComponent(value) + ";" + expires + ";path=/";
      } catch (e) {
        console.warn('Cookie not available, using storage');
        storage.set(name, value);
      }
    }

    function getCookie(name) {
      try {
        const cname = name + "=";
        const decodedCookie = decodeURIComponent(document.cookie);
        const ca = decodedCookie.split(';');
        for(let i = 0; i < ca.length; i++) {
          let c = ca[i].trim();
          if (c.indexOf(cname) === 0) {
            return c.substring(cname.length, c.length);
          }
        }
        return "";
      } catch (e) {
        console.warn('Cookie not available, using storage');
        return storage.get(name) || "";
      }
    }

    // Toggle unit cost edit functionality
    function toggleUnitCostEdit() {
      const unitCostInput = DOM.unitCostInput;
      const editIcon = document.getElementById('unitCostEditIcon');

      if (unitCostInput.readOnly) {
        // Enable editing
        unitCostInput.readOnly = false;
        unitCostInput.classList.remove('unit-cost-disabled');
        unitCostInput.style.backgroundColor = '';
        unitCostInput.style.color = '';
        unitCostInput.style.cursor = '';
        editIcon.textContent = '💾'; // Save icon
        editIcon.title = 'Click to save unit cost';
        unitCostInput.focus();
      } else {
        // Disable editing
        unitCostInput.readOnly = true;
        unitCostInput.classList.add('unit-cost-disabled');
        editIcon.textContent = '✏️'; // Edit icon
        editIcon.title = 'Click to edit unit cost';
      }
    }

    // Function to abbreviate room names (first letter + last character/number)
    function abbreviateRoomName(roomName) {
      if (!roomName || roomName === 'N/A' || roomName.length <= 2) {
        return roomName;
      }

      const firstChar = roomName.charAt(0);
      const lastChar = roomName.charAt(roomName.length - 1);

      return `${firstChar}${lastChar}`;
    }

    // Function to adjust column widths (disabled for mobile to maintain fixed widths)
    function adjustColumnWidthsForMobile() {
      // Skip adjustment on mobile screens to maintain fixed column widths
      if (window.innerWidth <= 650) {
        return;
      }

      // Only apply dynamic adjustments on larger screens if needed
      const tableRows = document.querySelectorAll('.div-table-row');
      if (tableRows.length === 0) return;

      // For screens larger than 650px, we can keep some dynamic behavior if needed
      // but for mobile (650px and below), we want fixed widths
    }

    function selectProduct(product) {
      DOM.productSearchInput.value = product.item_name;
      DOM.productSearchInput.dataset.itemCode = product.item_code; // Store the item_code in the dataset
      DOM.productSearchInput.dataset.roomId = product.room_id; // Store the room_id in the dataset
      DOM.productList.innerHTML = '';

      // Set the unit cost from the database
      DOM.unitCostInput.value = parseFloat(product.unit_cost || 0).toFixed(2);

      // Set the selling price from the database (unit_retail_price)
      // This can be modified by the user for negotiated prices or discounts
      DOM.priceInput.value = parseFloat(product.unit_retail_price || 0).toFixed(2);

      // Get the quantity input element
      const quantityInput = document.getElementById('quantity');

      // Set the minimum attribute to 1
      quantityInput.setAttribute('min', 1);

      // Set the default quantity to 1
      quantityInput.value = 1;

      DOM.roomInput.value = product.room_name;
      DOM.roomInput.classList.remove('hidden');
      DOM.roomLabel.classList.remove('hidden');
    }



    function addSelectedProduct() {
      console.log('addSelectedProduct function called');
      console.log('DOM elements:', {
        productSearchInput: DOM.productSearchInput,
        quantityInput: DOM.quantityInput,
        priceInput: DOM.priceInput,
        unitCostInput: DOM.unitCostInput
      });
      
      const productName = DOM.productSearchInput.value.trim();
      const quantity = parseInt(DOM.quantityInput.value);
      // Get the selling price from the input field - this may be the original retail price
      // or a negotiated/discounted price entered by the user
      const sellingPrice = parseFloat(DOM.priceInput.value) || 0;

      // Get unit cost from the input field
      const unitCost = parseFloat(DOM.unitCostInput.value) || 0;

      console.log('Input values:', { productName, quantity, sellingPrice, unitCost });

      // Get the item_code and room_id from the dataset if available
      const itemCode = DOM.productSearchInput.dataset.itemCode;
      const roomId = DOM.productSearchInput.dataset.roomId;

      // Find the product by item_code if available, otherwise fall back to finding by name
      let product;
      if (itemCode) {
        // Find the exact product by item_code (which is unique)
        product = allProducts.find(p => p.item_code === itemCode);
      } else {
        // Fall back to finding by name (for backward compatibility or manually entered products)
        product = allProducts.find(p => p.item_name.toLowerCase() === productName.toLowerCase());
      }

      if (productName === '' || isNaN(quantity) || isNaN(sellingPrice)) {
        console.log('Validation failed:', { productName: productName === '', quantityIsNaN: isNaN(quantity), sellingPriceIsNaN: isNaN(sellingPrice) });
        showToastMessage('Please enter valid product details.', 'error');
        return;
      }
      console.log('Validation passed - proceeding to add product');
      if (quantity <= 0) {
        console.log('Failed: Quantity <= 0');
        showToastMessage('Quantity must be at least 1.', 'error');
        return;
      }
      if (sellingPrice < 0) {
        console.log('Failed: Selling price < 0');
        showToastMessage('Selling price cannot be negative.', 'error');
        return;
      }
      if (unitCost < 0) {
        console.log('Failed: Unit cost < 0');
        showToastMessage('Unit cost cannot be negative.', 'error');
        return;
      }
      if (product && quantity > product.available_stock) {
        console.log('Failed: Insufficient stock');
        showToastMessage(`Cannot add ${quantity} units. Only ${product.available_stock} available in stock.`, 'error');
        return;
      }
      
      console.log('All validation checks passed - proceeding to tax calculation');

      // We'll store the selling price in product.price
      // Then let the table logic figure out net vs. tax if needed
      let taxPerProduct = 0;
      if (DOM.taxCheckbox.checked) {
        taxPerProduct = Math.round(sellingPrice * TAX_RATE);
      }
      
      console.log('Tax calculation completed. taxPerProduct:', taxPerProduct);
      console.log('Looking up product with itemCode:', itemCode);
      console.log('allProducts array length:', allProducts.length);
      console.log('Product lookup result:', product);
      console.log('selectedProducts array before:', selectedProducts.length);
      
      if (product) {
        console.log('Adding existing product');
        selectedProducts.push({
          item_code: product.item_code,
          room_id: product.room_id, // Store room_id for stock updates
          name: productName,
          room_name: product.room_name, // Store room name for reference
          quantity: quantity,
          price: parseFloat(sellingPrice), // price is the selling price
          unit_cost: unitCost,
          tax_per_product: taxPerProduct,
          is_new: false
        });
        product.available_stock -= quantity; // update stock
      } else {
        console.log('Adding new product');
        const item_code = `NEW-${newProductCounter++}`;
        selectedProducts.push({
          item_code: item_code,
          room_id: null, // No room for new products
          name: productName,
          room_name: 'N/A', // No room for new products
          quantity: quantity,
          price: parseFloat(sellingPrice), // price is the selling price
          unit_cost: unitCost,
          tax_per_product: taxPerProduct,
          is_new: true
        });
      }
      
      console.log('selectedProducts array after:', selectedProducts.length);
      console.log('Last added product:', selectedProducts[selectedProducts.length - 1]);

      console.log('About to call updateSelectedProductsTable');
      console.log('DOM.selectedProductsBody:', DOM.selectedProductsBody);
      updateSelectedProductsTable();
      console.log('updateSelectedProductsTable called');
      DOM.productSearchInput.value = '';
      DOM.productList.innerHTML = ''; // Clear the dropdown

      // Clear the stored item_code and room_id
      delete DOM.productSearchInput.dataset.itemCode;
      delete DOM.productSearchInput.dataset.roomId;

      // Reset quantity input field
      const quantityInput = document.getElementById('quantity');
      quantityInput.value = '1';
      quantityInput.setAttribute('min', '1');

      // Reset price, unit cost and room fields
      DOM.priceInput.value = '0';
      DOM.unitCostInput.value = '0';
      DOM.roomInput.value = '';
      DOM.roomInput.classList.add('hidden');
      DOM.roomLabel.classList.add('hidden');
    }

    /**
     * If tax is checked:
     *   - "Unit Price (R)" = price * 0.85
     *   - "Tax per Unit (R)" = price * 0.15
     *   - "Total (R)" = price * quantity
     *
     * If tax is NOT checked:
     *   - "Unit Price (R)" = price (as typed)
     *   - No separate tax column
     *   - "Total (R)" = price * quantity
     */
    function calculateAndDisplayTotal() {
      let subtotal = 0;
      let tax = 0;
      let total = 0;
      let totalProfit = 0;

      selectedProducts.forEach(product => {
        // Calculate profit for each product (price is the selling price)
        const sellingPrice = product.price;
        const unitCost = product.unit_cost || 0;
        const profitPerUnit = sellingPrice - unitCost;
        totalProfit += profitPerUnit * product.quantity;

        if (DOM.taxCheckbox.checked) {
          subtotal += (product.price * (1 - TAX_RATE)) * product.quantity;
          tax += (product.price * TAX_RATE) * product.quantity;
          total += (product.price * product.quantity);
        } else {
          subtotal += (product.price * product.quantity);
          total += (product.price * product.quantity);
        }
      });

      if (!DOM.taxCheckbox.checked) {
        tax = 0;
      }

      DOM.subtotalAmountSpan.textContent = subtotal.toFixed(0);
      DOM.taxAmountSpan.textContent = tax.toFixed(0);
      DOM.totalAmountSpan.textContent = total.toFixed(0);
      DOM.totalProfitAmountSpan.textContent = totalProfit.toFixed(2);
      DOM.totalProfitAmountSpan.style.color = totalProfit >= 0 ? '#28a745' : '#dc3545';
    }

    function updateTableHeader() {
      // Clear the header
      const header = DOM.tableHeader;
      header.innerHTML = '';

      // Product column
      const productHeader = document.createElement('div');
      productHeader.className = 'div-table-cell div-table-heading product-column';
      productHeader.textContent = 'Product';
      header.appendChild(productHeader);

      // QTY column
      const qtyHeader = document.createElement('div');
      qtyHeader.className = 'div-table-cell div-table-heading qty-column';
      qtyHeader.textContent = 'QTY';
      qtyHeader.style.fontWeight = '700';
      qtyHeader.style.textAlign = 'center';
      qtyHeader.style.fontSize = '13px';
      header.appendChild(qtyHeader);

      // Price column (simplified name)
      const priceHeader = document.createElement('div');
      priceHeader.className = 'div-table-cell div-table-heading price-column';
      priceHeader.textContent = 'PRICE';
      priceHeader.style.fontSize = '13px';
      header.appendChild(priceHeader);

      // Unit Cost column
      const costHeader = document.createElement('div');
      costHeader.className = 'div-table-cell div-table-heading price-column';
      costHeader.textContent = 'COST';
      costHeader.style.fontSize = '13px';
      header.appendChild(costHeader);

      // Profit column
      const profitHeader = document.createElement('div');
      profitHeader.className = 'div-table-cell div-table-heading price-column';
      profitHeader.textContent = 'PROFIT';
      profitHeader.style.fontSize = '13px';
      header.appendChild(profitHeader);

      if (DOM.taxCheckbox.checked) {
        // Tax column (only if tax is checked, simplified name)
        const taxHeader = document.createElement('div');
        taxHeader.className = 'div-table-cell div-table-heading price-column tax-column';
        taxHeader.textContent = 'TAX';
        taxHeader.style.fontSize = '13px';
        header.appendChild(taxHeader);
      }

      // Total column
      const totalHeader = document.createElement('div');
      totalHeader.className = 'div-table-cell div-table-heading price-column';
      totalHeader.textContent = 'TOTAL';
      totalHeader.style.fontSize = '13px';
      header.appendChild(totalHeader);

      // Action column (X)
      const actionHeader = document.createElement('div');
      actionHeader.className = 'div-table-cell div-table-heading';
      actionHeader.innerHTML = '&nbsp;'; // Empty header for the X column
      actionHeader.style.textAlign = 'center';
      actionHeader.style.width = '40px'; // Fixed width for X column
      header.appendChild(actionHeader);
    }

    function removeProduct(index) {
      const removedProduct = selectedProducts.splice(index, 1)[0];
      updateSelectedProductsTable();

      // Restore stock if it was an existing product
      if (!removedProduct.is_new && removedProduct && allProducts.length > 0) {
        const product = allProducts.find(p => p.item_code === removedProduct.item_code);
        if (product) {
          product.available_stock += removedProduct.quantity;
        }
      }

      showToastMessage(`Removed ${removedProduct.name} from the selection.`, 'info');
    }

    function generateReceipt() {
      console.log('Starting generateReceipt function');
      const selectedCompanyCookie = getCookie('selectedCompany');
      if (!selectedCompanyCookie) {
        showToastMessage('Please select a company before generating a receipt.', 'error');
        return;
      }
      // Always ensure email has the default value if empty
      const customerEmail = DOM.customerEmailInput.value.trim();
      const billingInfo = {
        name: DOM.customerNameInput.value.trim(),
        email: customerEmail || '<EMAIL>',
        address: DOM.customerAddressInput.value.trim(),
        phone: DOM.customerPhoneInput.value.trim()
      };
      console.log('Billing info:', billingInfo);

      let shippingInfo = null;
      if (!DOM.sameAsBillingCheckbox.checked) {
        // Always ensure shipping email has the default value if empty
        const shippingEmail = DOM.shippingEmailInput.value.trim();
        shippingInfo = {
          name: DOM.shippingNameInput.value.trim(),
          email: shippingEmail || '<EMAIL>',
          address: DOM.shippingAddressInput.value.trim(),
          phone: DOM.shippingPhoneInput.value.trim()
        };
      }

      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }
      console.log('Products validation passed');

      const paymentMethod = DOM.paymentMethodSelect.value;
      const comments = DOM.commentsTextarea.value.trim();
      const salespersonName = DOM.salespersonNameInput.value.trim();
      console.log('Salesperson name:', salespersonName);

      const customerInfo = {
        billing: billingInfo,
        shipping: DOM.sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: paymentMethod,
        comments: comments,
        salespersonName: salespersonName
      };

      const selectedCompany = JSON.parse(selectedCompanyCookie);
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(DOM.subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(DOM.taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(DOM.totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
      console.log('All cookies set, redirecting to receipt.html');

      window.location.href = 'receipt.html';
    }

    function generateQuotation() {
      console.log('Starting generateQuotation function');
      const selectedCompanyCookie = getCookie('selectedCompany');
      if (!selectedCompanyCookie) {
        showToastMessage('Please select a company before generating a quotation.', 'error');
        return;
      }

      // Always ensure email has the default value if empty
      const customerEmail = DOM.customerEmailInput.value.trim();
      const billingInfo = {
        name: DOM.customerNameInput.value.trim(),
        email: customerEmail || '<EMAIL>',
        address: DOM.customerAddressInput.value.trim(),
        phone: DOM.customerPhoneInput.value.trim()
      };

      let shippingInfo = null;
      if (!DOM.sameAsBillingCheckbox.checked) {
        // Always ensure shipping email has the default value if empty
        const shippingEmail = DOM.shippingEmailInput.value.trim();
        shippingInfo = {
          name: DOM.shippingNameInput.value.trim(),
          email: shippingEmail || '<EMAIL>',
          address: DOM.shippingAddressInput.value.trim(),
          phone: DOM.shippingPhoneInput.value.trim()
        };
      }

      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }

      const comments = DOM.commentsTextarea.value.trim();
      const salespersonName = DOM.salespersonNameInput.value.trim();

      const customerInfo = {
        billing: billingInfo,
        shipping: DOM.sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: 'N/A',
        comments: comments,
        salespersonName: salespersonName,
        timestamp: Date.now().toString() // Add timestamp to track when this quotation was created
      };

      const selectedCompany = JSON.parse(selectedCompanyCookie);
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(DOM.subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(DOM.taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(DOM.totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
      console.log('All cookies set, redirecting to quotation.html');

      window.location.href = 'quotation.html';
    }

    function generateInvoice() {
      console.log('Starting generateInvoice function');
      const selectedCompanyCookie = getCookie('selectedCompany');
      if (!selectedCompanyCookie) {
        showToastMessage('Please select a company before generating an invoice.', 'error');
        return;
      }

      // Always ensure email has the default value if empty
      const customerEmail = DOM.customerEmailInput.value.trim();
      const billingInfo = {
        name: DOM.customerNameInput.value.trim(),
        email: customerEmail || '<EMAIL>',
        address: DOM.customerAddressInput.value.trim(),
        phone: DOM.customerPhoneInput.value.trim()
      };

      let shippingInfo = null;
      if (!DOM.sameAsBillingCheckbox.checked) {
        // Always ensure shipping email has the default value if empty
        const shippingEmail = DOM.shippingEmailInput.value.trim();
        shippingInfo = {
          name: DOM.shippingNameInput.value.trim(),
          email: shippingEmail || '<EMAIL>',
          address: DOM.shippingAddressInput.value.trim(),
          phone: DOM.shippingPhoneInput.value.trim()
        };
      }

      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }

      const paymentMethod = DOM.paymentMethodSelect.value;
      const comments = DOM.commentsTextarea.value.trim();
      const salespersonName = DOM.salespersonNameInput.value.trim();

      const customerInfo = {
        billing: billingInfo,
        shipping: DOM.sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: paymentMethod,
        comments: comments,
        salespersonName: salespersonName
      };

      const selectedCompany = JSON.parse(selectedCompanyCookie);
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(DOM.subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(DOM.taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(DOM.totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
      console.log('All cookies set, redirecting to invoice.html');

      window.location.href = 'invoice.html';
    }

    function toggleShippingInfo() {
      if (DOM.sameAsBillingCheckbox.checked) {
        DOM.shippingInfoDiv.classList.add('hidden');
      } else {
        DOM.shippingInfoDiv.classList.remove('hidden');
      }
    }

    function setCompanyInfo() {
      const selectedCompanyKey = DOM.companySelect.value;
      if (!selectedCompanyKey || !companies[selectedCompanyKey]) {
        showToastMessage('Please select a valid company.', 'error');
        return;
      }
      const selectedCompany = companies[selectedCompanyKey];
      setCookie('selectedCompany', JSON.stringify({
        key: selectedCompanyKey,
        name: selectedCompany.name,
        bankingInformation: selectedCompany.bankingInformation
      }), 1);
    }

    DOM.companySelect.addEventListener('change', setCompanyInfo);

    // Recalculate net/tax for each product if the checkbox changes
    DOM.taxCheckbox.addEventListener('change', () => {
      updateSelectedProductsTable();
    });

    // Function to load existing order data when returning from document pages
    function loadExistingOrderData() {
      // Check if we're in edit mode (coming back from a document page)
      const isEditingOrder = sessionStorage.getItem('editingOrder') === 'true';

      if (isEditingOrder) {
        // Clear the editing flag
        sessionStorage.removeItem('editingOrder');

        // Load customer info
        const customerInfoCookie = getCookie('customerInfo');
        if (customerInfoCookie) {
          const customerInfo = JSON.parse(customerInfoCookie);

          // Fill in customer information
          DOM.customerNameInput.value = customerInfo.billing.name || '';
          DOM.customerAddressInput.value = customerInfo.billing.address || '';
          DOM.customerEmailInput.value = customerInfo.billing.email || '';
          DOM.customerPhoneInput.value = customerInfo.billing.phone || '';

          // Handle shipping info
          if (customerInfo.shipping) {
            DOM.sameAsBillingCheckbox.checked = false;
            DOM.shippingInfoDiv.classList.remove('hidden');
            DOM.shippingNameInput.value = customerInfo.shipping.name || '';
            DOM.shippingAddressInput.value = customerInfo.shipping.address || '';
            DOM.shippingEmailInput.value = customerInfo.shipping.email || '';
            DOM.shippingPhoneInput.value = customerInfo.shipping.phone || '';
          } else {
            DOM.sameAsBillingCheckbox.checked = true;
            DOM.shippingInfoDiv.classList.add('hidden');
          }

          // Set payment method
          if (customerInfo.paymentMethod) {
            DOM.paymentMethodSelect.value = customerInfo.paymentMethod;
          }

          // Set comments
          if (customerInfo.comments) {
            DOM.commentsTextarea.value = customerInfo.comments;
          }

          // Set salesperson name
          if (customerInfo.salespersonName) {
            DOM.salespersonNameInput.value = customerInfo.salespersonName;
          }
        }

        // Load selected products
        const selectedProductsCookie = getCookie('selectedProducts');
        if (selectedProductsCookie) {
          selectedProducts = JSON.parse(selectedProductsCookie);
          updateSelectedProductsTable();
        }

        // Show a message to the user
        showToastMessage('Order loaded for editing. You can modify products and details before generating a new document.', 'info');
      }
    }

    // Add polyfill for AbortController if not available
    if (typeof AbortController === 'undefined') {
      window.AbortController = function() {
        this.signal = {
          aborted: false
        };
        this.abort = function() {
          this.signal.aborted = true;
        };
      };
    }



    // Add polyfill for fetch if not available
    if (typeof fetch === 'undefined') {
      window.fetch = function(url, options) {
        return new Promise((resolve, reject) => {
          const xhr = new XMLHttpRequest();
          xhr.open(options.method || 'GET', url);

          if (options.headers) {
            Object.keys(options.headers).forEach(key => {
              xhr.setRequestHeader(key, options.headers[key]);
            });
          }

          xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
              resolve({
                ok: true,
                status: xhr.status,
                json: () => Promise.resolve(JSON.parse(xhr.responseText))
              });
            } else {
              reject({
                ok: false,
                status: xhr.status,
                statusText: xhr.statusText
              });
            }
          };

          xhr.onerror = function() {
            reject({
              ok: false,
              status: 0,
              statusText: 'Network Error'
            });
          };

          if (options.signal) {
            options.signal.addEventListener('abort', () => {
              xhr.abort();
              reject({
                name: 'AbortError',
                message: 'The operation was aborted'
              });
            });
          }

          xhr.send(options.body);
        });
      };
    }
  </script>

</body>
</html>
