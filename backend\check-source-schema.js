// Check Source Database Schema
// Examines the actual schema of tables in the source database

const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function checkTableSchema(tableName) {
  const client = await pool.connect();
  
  try {
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = $1
      ORDER BY ordinal_position
    `, [tableName]);
    
    console.log(`\n📋 Table: ${tableName}`);
    console.log('Columns:');
    for (const row of result.rows) {
      console.log(`  - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'} ${row.column_default ? `DEFAULT ${row.column_default}` : ''}`);
    }
    
  } catch (error) {
    console.error(`❌ Error checking ${tableName}:`, error.message);
  } finally {
    client.release();
  }
}

async function checkAllSchemas() {
  console.log('🔍 Checking source database schemas...');
  
  const tables = [
    'users', 'rooms', 'expense_categories', 'categories', 'car_brands', 
    'car_models', 'products', 'sales', 'sale_items', 'expenses', 
    'quotations', 'quotation_items', 'invoices', 'invoice_items',
    'recurring_expense_exclusions'
  ];
  
  for (const table of tables) {
    await checkTableSchema(table);
  }
  
  await pool.end();
}

checkAllSchemas().catch(console.error);
