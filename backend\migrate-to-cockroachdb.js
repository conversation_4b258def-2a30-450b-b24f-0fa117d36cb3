// CockroachDB Migration Script
// Migrates data from current Render.com database to new CockroachDB

const { Pool } = require('pg');
require('dotenv').config();

// Source database connection (current Render.com)
const sourceDbConfig = {
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
};

// Target database connection (CockroachDB)
const targetDbConfig = {
  connectionString: "postgresql://shans:<EMAIL>:26257/defaultdb?sslmode=verify-full",
  ssl: { rejectUnauthorized: false }
};

const sourcePool = new Pool(sourceDbConfig);
const targetPool = new Pool(targetDbConfig);

// Tables to migrate in order (respecting foreign key dependencies)
const TABLES_TO_MIGRATE = [
  'users',
  'rooms', 
  'expense_categories',
  'categories',
  'car_brands',
  'car_models',
  'products',
  'sales',
  'sale_items',
  'expenses',
  'quotations',
  'quotation_items',
  'invoices',
  'invoice_items',
  'recurring_expense_exclusions'
];

async function testConnections() {
  console.log('🔗 Testing database connections...');
  
  try {
    // Test source connection
    const sourceClient = await sourcePool.connect();
    const sourceResult = await sourceClient.query('SELECT version()');
    console.log('✅ Source database (Render.com) connected');
    sourceClient.release();
    
    // Test target connection
    const targetClient = await targetPool.connect();
    const targetResult = await targetClient.query('SELECT version()');
    console.log('✅ Target database (CockroachDB) connected');
    console.log('   Version:', targetResult.rows[0].version.substring(0, 50) + '...');
    targetClient.release();
    
    return true;
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
    return false;
  }
}

async function createTablesInCockroachDB() {
  console.log('🏗️  Creating tables in CockroachDB...');
  
  const targetClient = await targetPool.connect();
  
  try {
    // CockroachDB-compatible table creation queries
    const tableQueries = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        is_admin BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Rooms table
      `CREATE TABLE IF NOT EXISTS rooms (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        color VARCHAR(50) NOT NULL,
        location VARCHAR(255) NOT NULL
      )`,
      
      // Expense categories table
      `CREATE TABLE IF NOT EXISTS expense_categories (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Categories table
      `CREATE TABLE IF NOT EXISTS categories (
        id SERIAL PRIMARY KEY,
        category_id VARCHAR(50) NOT NULL,
        name VARCHAR(255) NOT NULL
      )`,
      
      // Car brands table
      `CREATE TABLE IF NOT EXISTS car_brands (
        id SERIAL PRIMARY KEY,
        brand_id VARCHAR(50) NOT NULL,
        name VARCHAR(255) NOT NULL
      )`,
      
      // Car models table
      `CREATE TABLE IF NOT EXISTS car_models (
        id SERIAL PRIMARY KEY,
        model_id VARCHAR(50) NOT NULL,
        name VARCHAR(255) NOT NULL,
        brand_id VARCHAR(50) NOT NULL
      )`
    ];
    
    for (const query of tableQueries) {
      await targetClient.query(query);
    }
    
    console.log('✅ Basic tables created successfully in CockroachDB');
  } catch (error) {
    console.error('❌ Error creating tables:', error.message);
    throw error;
  } finally {
    targetClient.release();
  }
}

async function migrateTableData(tableName) {
  console.log(`📦 Migrating table: ${tableName}`);
  
  const sourceClient = await sourcePool.connect();
  const targetClient = await targetPool.connect();
  
  try {
    // Get data from source
    const sourceResult = await sourceClient.query(`SELECT * FROM ${tableName} ORDER BY 1`);
    const rows = sourceResult.rows;
    
    if (rows.length === 0) {
      console.log(`   ℹ️  ${tableName}: No data to migrate`);
      return { table: tableName, migrated: 0, skipped: 0 };
    }
    
    // Clear target table first
    await targetClient.query(`DELETE FROM ${tableName}`);
    
    // Get column names
    const columns = Object.keys(rows[0]);
    const columnList = columns.join(', ');
    const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');
    
    const insertQuery = `INSERT INTO ${tableName} (${columnList}) VALUES (${placeholders})`;
    
    let migrated = 0;
    
    // Insert data in batches
    for (const row of rows) {
      const values = columns.map(col => row[col]);
      await targetClient.query(insertQuery, values);
      migrated++;
    }
    
    console.log(`   ✅ ${tableName}: ${migrated} records migrated`);
    return { table: tableName, migrated, skipped: 0 };
    
  } catch (error) {
    console.error(`   ❌ ${tableName}: Migration failed - ${error.message}`);
    throw error;
  } finally {
    sourceClient.release();
    targetClient.release();
  }
}

async function createComplexTables() {
  console.log('🏗️  Creating complex tables in CockroachDB...');

  const targetClient = await targetPool.connect();

  try {
    const complexTableQueries = [
      // Products table
      `CREATE TABLE IF NOT EXISTS products (
        item_code VARCHAR(100) NOT NULL,
        room_id INTEGER NOT NULL,
        item_name VARCHAR(255) NOT NULL,
        car_brand VARCHAR(100) NOT NULL,
        car_model VARCHAR(100) NOT NULL,
        unit_retail_price DECIMAL(10,2) NOT NULL,
        wholesale_price DECIMAL(10,2) NOT NULL,
        unit_cost DECIMAL(10,2) NOT NULL,
        supplier_code VARCHAR(100) NOT NULL,
        available_stock INTEGER NOT NULL,
        location VARCHAR(255) NOT NULL,
        colour_tape VARCHAR(50) NOT NULL,
        profit DECIMAL(10,2) NOT NULL,
        additional_comments TEXT,
        product_category VARCHAR(100) NOT NULL,
        min_order_quantity INTEGER DEFAULT 1,
        low_stock_threshold INTEGER DEFAULT 5,
        PRIMARY KEY (item_code, room_id),
        FOREIGN KEY(room_id) REFERENCES rooms(id) ON DELETE CASCADE
      )`,

      // Sales table
      `CREATE TABLE IF NOT EXISTS sales (
        id SERIAL PRIMARY KEY,
        reference_number VARCHAR(100) UNIQUE NOT NULL,
        date DATE NOT NULL,
        customer_name VARCHAR(255) NOT NULL,
        customer_email VARCHAR(255),
        customer_phone VARCHAR(50),
        customer_address TEXT,
        subtotal DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_method VARCHAR(50),
        payment_status VARCHAR(50) DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Sale items table
      `CREATE TABLE IF NOT EXISTS sale_items (
        id SERIAL PRIMARY KEY,
        sale_id INTEGER NOT NULL,
        item_code VARCHAR(100) NOT NULL,
        item_name VARCHAR(255) NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY(sale_id) REFERENCES sales(id) ON DELETE CASCADE
      )`,

      // Expenses table
      `CREATE TABLE IF NOT EXISTS expenses (
        id SERIAL PRIMARY KEY,
        category_id INTEGER NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        description TEXT NOT NULL,
        date DATE NOT NULL,
        receipt_url VARCHAR(500),
        is_recurring BOOLEAN DEFAULT false,
        recurring_template_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY(category_id) REFERENCES expense_categories(id),
        FOREIGN KEY(recurring_template_id) REFERENCES expenses(id)
      )`,

      // Quotations table
      `CREATE TABLE IF NOT EXISTS quotations (
        id SERIAL PRIMARY KEY,
        reference_number VARCHAR(100) UNIQUE NOT NULL,
        date DATE NOT NULL,
        customer_name VARCHAR(255) NOT NULL,
        customer_email VARCHAR(255),
        customer_phone VARCHAR(50),
        customer_address TEXT,
        subtotal DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        valid_until DATE,
        status VARCHAR(50) DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Quotation items table
      `CREATE TABLE IF NOT EXISTS quotation_items (
        id SERIAL PRIMARY KEY,
        quotation_id INTEGER NOT NULL,
        room_id INTEGER NOT NULL,
        item_code VARCHAR(100) NOT NULL,
        item_name VARCHAR(255) NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY(quotation_id) REFERENCES quotations(id) ON DELETE CASCADE,
        FOREIGN KEY(room_id) REFERENCES rooms(id)
      )`,

      // Invoices table
      `CREATE TABLE IF NOT EXISTS invoices (
        id SERIAL PRIMARY KEY,
        reference_number VARCHAR(100) UNIQUE NOT NULL,
        date DATE NOT NULL,
        due_date DATE,
        customer_name VARCHAR(255) NOT NULL,
        customer_email VARCHAR(255),
        customer_phone VARCHAR(50),
        customer_address TEXT,
        subtotal DECIMAL(10,2) NOT NULL,
        tax_amount DECIMAL(10,2) DEFAULT 0,
        discount_amount DECIMAL(10,2) DEFAULT 0,
        total_amount DECIMAL(10,2) NOT NULL,
        payment_status VARCHAR(50) DEFAULT 'pending',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Invoice items table
      `CREATE TABLE IF NOT EXISTS invoice_items (
        id SERIAL PRIMARY KEY,
        invoice_id INTEGER NOT NULL,
        room_id INTEGER NOT NULL,
        item_code VARCHAR(100) NOT NULL,
        item_name VARCHAR(255) NOT NULL,
        quantity INTEGER NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        total_price DECIMAL(10,2) NOT NULL,
        FOREIGN KEY(invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
        FOREIGN KEY(room_id) REFERENCES rooms(id)
      )`,

      // Recurring expense exclusions table
      `CREATE TABLE IF NOT EXISTS recurring_expense_exclusions (
        id SERIAL PRIMARY KEY,
        recurring_template_id INTEGER NOT NULL,
        excluded_date DATE NOT NULL,
        reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY(recurring_template_id) REFERENCES expenses(id) ON DELETE CASCADE
      )`
    ];

    for (const query of complexTableQueries) {
      await targetClient.query(query);
    }

    console.log('✅ Complex tables created successfully in CockroachDB');
  } catch (error) {
    console.error('❌ Error creating complex tables:', error.message);
    throw error;
  } finally {
    targetClient.release();
  }
}

async function fixSequences() {
  console.log('🔧 Fixing sequences in CockroachDB...');

  const targetClient = await targetPool.connect();

  try {
    // CockroachDB sequence fixes
    const sequenceFixes = [
      "SELECT setval('users_id_seq', COALESCE((SELECT MAX(id) FROM users), 0) + 1, false)",
      "SELECT setval('rooms_id_seq', COALESCE((SELECT MAX(id) FROM rooms), 0) + 1, false)",
      "SELECT setval('expense_categories_id_seq', COALESCE((SELECT MAX(id) FROM expense_categories), 0) + 1, false)",
      "SELECT setval('categories_id_seq', COALESCE((SELECT MAX(id) FROM categories), 0) + 1, false)",
      "SELECT setval('car_brands_id_seq', COALESCE((SELECT MAX(id) FROM car_brands), 0) + 1, false)",
      "SELECT setval('car_models_id_seq', COALESCE((SELECT MAX(id) FROM car_models), 0) + 1, false)",
      "SELECT setval('sales_id_seq', COALESCE((SELECT MAX(id) FROM sales), 0) + 1, false)",
      "SELECT setval('sale_items_id_seq', COALESCE((SELECT MAX(id) FROM sale_items), 0) + 1, false)",
      "SELECT setval('expenses_id_seq', COALESCE((SELECT MAX(id) FROM expenses), 0) + 1, false)",
      "SELECT setval('quotations_id_seq', COALESCE((SELECT MAX(id) FROM quotations), 0) + 1, false)",
      "SELECT setval('quotation_items_id_seq', COALESCE((SELECT MAX(id) FROM quotation_items), 0) + 1, false)",
      "SELECT setval('invoices_id_seq', COALESCE((SELECT MAX(id) FROM invoices), 0) + 1, false)",
      "SELECT setval('invoice_items_id_seq', COALESCE((SELECT MAX(id) FROM invoice_items), 0) + 1, false)",
      "SELECT setval('recurring_expense_exclusions_id_seq', COALESCE((SELECT MAX(id) FROM recurring_expense_exclusions), 0) + 1, false)"
    ];

    for (const fix of sequenceFixes) {
      try {
        await targetClient.query(fix);
      } catch (error) {
        console.log(`   ⚠️  Sequence fix warning: ${error.message}`);
      }
    }

    console.log('✅ Sequences fixed successfully');
  } catch (error) {
    console.error('❌ Error fixing sequences:', error.message);
    throw error;
  } finally {
    targetClient.release();
  }
}

async function runMigration() {
  console.log('🚀 Starting CockroachDB Migration');
  console.log('=====================================');

  const startTime = Date.now();

  try {
    // Test connections
    const connectionsOk = await testConnections();
    if (!connectionsOk) {
      throw new Error('Database connections failed');
    }

    console.log('');

    // Create basic tables
    await createTablesInCockroachDB();

    console.log('');

    // Create complex tables with foreign keys
    await createComplexTables();

    console.log('');

    // Migrate data table by table
    const migrationResults = {};

    for (const tableName of TABLES_TO_MIGRATE) {
      try {
        const result = await migrateTableData(tableName);
        migrationResults[tableName] = result;
      } catch (error) {
        console.error(`❌ Failed to migrate ${tableName}:`, error.message);
        migrationResults[tableName] = { error: error.message };
      }
    }

    console.log('');

    // Fix sequences
    await fixSequences();

    console.log('');

    // Summary
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log('🎉 Migration Summary');
    console.log('===================');
    console.log(`⏱️  Duration: ${duration} seconds`);

    let totalMigrated = 0;
    for (const [table, result] of Object.entries(migrationResults)) {
      if (result.error) {
        console.log(`❌ ${table}: ${result.error}`);
      } else {
        console.log(`✅ ${table}: ${result.migrated} records`);
        totalMigrated += result.migrated;
      }
    }

    console.log(`📊 Total records migrated: ${totalMigrated}`);
    console.log('✅ Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    throw error;
  } finally {
    await sourcePool.end();
    await targetPool.end();
  }
}

// Run the migration
if (require.main === module) {
  runMigration().catch(console.error);
}

module.exports = { runMigration, testConnections, createTablesInCockroachDB, createComplexTables, migrateTableData, fixSequences };
