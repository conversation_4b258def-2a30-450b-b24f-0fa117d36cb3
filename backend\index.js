// backend/server.js
require('dotenv').config(); // Load environment variables
// Use default Playwright browser installation path
// process.env.PLAYWRIGHT_BROWSERS_PATH = "0";

// Check Node.js version
const nodeVersion = process.version;
console.log(`Running on Node.js ${nodeVersion}`);

// Warn about potentially incompatible Node.js version
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0], 10);
if (majorVersion > 18) {
  console.warn('WARNING: You are running on Node.js version', nodeVersion);
  console.warn('This application was developed and tested with Node.js v18.x');
  console.warn('Some dependencies may not be fully compatible with your Node.js version');
  console.warn('If you experience issues, consider downgrading to Node.js v18.x');
  console.warn('You can use nvm to install and switch to Node.js v18.x:');
  console.warn('  nvm install 18');
  console.warn('  nvm use 18');
}

const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const path = require('path');
const ejs = require('ejs');
const session = require('express-session');
// Removed Puppeteer in favor of Playwright
const { chromium } = require('playwright');
const nodemailer = require('nodemailer');
const fs = require('fs');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

// (Optional) You can remove this if you're not using any custom caching options
// const puppeteerConfig = require('./puppeteer.config.cjs');

// Import your models
const roomModel = require('./models/roomModel');
const productModel = require('./models/productModel');
const salesModel = require('./models/salesModel');
const categoryModel = require('./models/categoryModel');
const carBrandModel = require('./models/carBrandModel');
const carModelModel = require('./models/carModelModel');
const UserModel = require('./models/userModel');
const expenseModel = require('./models/expenseModel');
const expenseCategoryModel = require('./models/expenseCategoryModel');
const quotationModel = require('./models/quotationModel');
const invoiceModel = require('./models/invoiceModel');

// Import authentication middleware
const { authenticateToken, requireAdmin, generateToken } = require('./middleware/auth');

// Import backup routes
const backupRoutes = require('./routes/backupRoutes');

// Import expense media routes
const expenseMediaRoutes = require('./routes/expenseMediaRoutes');

// Import database pool
const pool = require('./database');

const app = express();
const PORT = 8001;
// Only trust specific proxies if needed
app.set('trust proxy', false);

// -------------------- Middleware Setup -------------------- //
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

// Session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret-change-in-production',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: false, // Set to true in production with HTTPS
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// CORS Configuration - Allow specific origins for production
const allowedOrigins = [
  'http://localhost:3000',
  'http://localhost:8080',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:8080',
  'http://127.0.0.1:5500',
  'https://shans-system.netlify.app',
  'https://shans-backend.onrender.com'
];

app.use(cors({
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Cache-Control',
    'Pragma'
  ],
  credentials: true,
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
}));


// Handle preflight requests
app.options('*', (req, res) => {
  res.header('Access-Control-Allow-Origin', req.headers.origin);
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Cache-Control, Pragma');
  res.header('Access-Control-Allow-Credentials', 'true');
  res.sendStatus(200);
});

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use('/static', express.static(path.join(__dirname, 'public')));

// Rate Limiting for PDF Generation Endpoints
const pdfLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 100,
  message: 'Too many PDF generation requests from this IP, please try again later.',
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  // Since we set trust proxy to false, we don't need to configure trustProxy here
});

// -------------------- Nodemailer Configuration -------------------- //
const transporter = nodemailer.createTransport({
  service: process.env.EMAIL_SERVICE,
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASS,
  },
});

// Verify the transporter configuration
transporter.verify((error, success) => {
  if (error) {
    console.error('Error configuring Nodemailer transporter:', error);
  } else {
    console.log('Nodemailer transporter is configured correctly.');
  }
});

// -------------------- Helper Functions -------------------- //
/**
 * Helper Function to Render EJS Template and Generate PDF
 */
async function renderPdf(template, data) {
  try {
    console.log(`[${new Date().toISOString()}] [DEBUG] Rendering PDF template: ${template}`);
    console.log(`[${new Date().toISOString()}] [DEBUG] Template data keys:`, Object.keys(data));
    console.log(`[${new Date().toISOString()}] [DEBUG] Salesperson name in renderPdf:`, data.salespersonName);

    const html = await ejs.renderFile(
      path.join(__dirname, 'views', `${template}.ejs`),
      data
    );

    // Launch Playwright's Chromium browser in headless mode.
    // Since PLAYWRIGHT_BROWSERS_PATH is set to "0", it will use the browser from the node_modules folder.
    const browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    // Set the content and wait for network activity to idle.
    await page.setContent(html, { waitUntil: 'networkidle' });
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '20px', bottom: '20px', left: '20px', right: '20px' },
    });
    await browser.close();
    return pdfBuffer;
  } catch (error) {
    console.error('Error in renderPdf:', error);
    throw error;
  }
}

// -------------------- Initialize Users Table -------------------- //
(async () => {
  try {
    await UserModel.initializeUsersTable();
    console.log('Users table initialized successfully');
  } catch (error) {
    console.error('Failed to initialize users table:', error);
  }
})();

// -------------------- Authentication Routes -------------------- //

// Login route
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    const user = await UserModel.getUserByEmail(email);
    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const isValidPassword = await UserModel.verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const token = generateToken(user.id);

    // Store user info in session
    req.session.userId = user.id;
    req.session.isAdmin = user.is_admin;

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        is_admin: user.is_admin
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Logout route
app.post('/api/auth/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      return res.status(500).json({ message: 'Could not log out' });
    }
    res.json({ message: 'Logout successful' });
  });
});

// Check authentication status
app.get('/api/auth/me', authenticateToken, async (req, res) => {
  res.json({
    user: {
      id: req.user.id,
      email: req.user.email,
      is_admin: req.user.is_admin
    }
  });
});

// -------------------- User Management Routes (Admin Only) -------------------- //

// Get all users
app.get('/api/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const users = await UserModel.getAllUsers();
    res.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Create new user
app.post('/api/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { email, password, is_admin } = req.body;

    if (!email || !password) {
      return res.status(400).json({ message: 'Email and password are required' });
    }

    // Check if user already exists
    const existingUser = await UserModel.getUserByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    const newUser = await UserModel.createUser(email, password, is_admin || false);
    res.status(201).json(newUser);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Update user
app.put('/api/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { email, is_admin } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    const updatedUser = await UserModel.updateUser(userId, email, is_admin || false);
    if (!updatedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Update user password
app.put('/api/users/:id/password', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({ message: 'Password is required' });
    }

    await UserModel.updateUserPassword(userId, password);
    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Error updating password:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Delete user
app.delete('/api/users/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = parseInt(req.params.id);

    // Prevent admin from deleting themselves
    if (userId === req.user.id) {
      return res.status(400).json({ message: 'Cannot delete your own account' });
    }

    const success = await UserModel.deleteUser(userId);
    if (!success) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// -------------------- Routes -------------------- //

// Room Routes
app.get('/api/rooms', async (req, res) => {
  try {
    const rooms = await roomModel.getAllRooms();
    res.json(rooms);
  } catch (error) {
    console.error('Error retrieving rooms:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/rooms/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const room = await roomModel.getRoomById(id);
    if (room) {
      res.json(room);
    } else {
      res.status(404).json({ message: 'Room not found' });
    }
  } catch (error) {
    console.error('Error retrieving room:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/rooms', async (req, res) => {
  try {
    const { name, color, location } = req.body;
    if (!name || !color || !location) {
      return res
        .status(400)
        .json({ message: 'Name, color, and location are required' });
    }
    const newRoom = await roomModel.createRoom({ name, color, location });
    res.status(201).json(newRoom);
  } catch (error) {
    console.error('Error creating room:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.put('/api/rooms/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const { name, color, location } = req.body;
    if (!name || !color || !location) {
      return res
        .status(400)
        .json({ message: 'Name, color, and location are required' });
    }
    const updatedRoom = await roomModel.updateRoom(id, {
      name,
      color,
      location,
    });
    if (updatedRoom) {
      res.json(updatedRoom);
    } else {
      res.status(404).json({ message: 'Room not found' });
    }
  } catch (error) {
    console.error('Error updating room:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Store verification codes temporarily (in production, use Redis or database)
const verificationCodes = new Map();

// Clean up expired verification codes every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of verificationCodes.entries()) {
    if (now > data.expires) {
      verificationCodes.delete(key);
      console.log(`Cleaned up expired verification code: ${key}`);
    }
  }
}, 5 * 60 * 1000); // 5 minutes

// Request room deletion verification code
app.post('/api/rooms/:id/request-deletion', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const room = await roomModel.getRoomById(id);
    if (!room) {
      return res.status(404).json({ message: 'Room not found' });
    }

    // Generate 6-digit verification code
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();

    // Store code with expiration (5 minutes)
    const expirationTime = Date.now() + 5 * 60 * 1000; // 5 minutes
    verificationCodes.set(`room_${id}`, {
      code: verificationCode,
      expires: expirationTime,
      roomName: room.name
    });

    // Send verification email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: '<EMAIL>', // Updated email address
      subject: 'Room Deletion Verification Code',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #d32f2f;">Room Deletion Verification</h2>
          <p>A request has been made to delete the following room:</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
            <strong>Room Name:</strong> ${room.name}<br>
            <strong>Location:</strong> ${room.location}<br>
            <strong>Requested by:</strong> ${req.user.email}<br>
            <strong>Time:</strong> ${new Date().toLocaleString()}
          </div>
          <p><strong>Verification Code:</strong></p>
          <div style="background-color: #e3f2fd; padding: 20px; text-align: center; border-radius: 5px; margin: 15px 0;">
            <span style="font-size: 24px; font-weight: bold; color: #1976d2; letter-spacing: 3px;">${verificationCode}</span>
          </div>
          <p style="color: #666; font-size: 14px;">
            This code will expire in 5 minutes. If you did not request this deletion, please ignore this email.
          </p>
          <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 20px; color: #666; font-size: 12px;">
            <p>This is an automated message from Shans System Admin Panel.</p>
          </div>
        </div>
      `
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error('Error sending verification email:', error);
        return res.status(500).json({ message: 'Failed to send verification email.' });
      }
      console.log('Verification email sent:', info.response);
      res.json({
        message: 'Verification code <NAME_EMAIL>',
        expiresIn: 300 // 5 minutes in seconds
      });
    });

  } catch (error) {
    console.error('Error requesting room deletion:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Verify code and delete room
app.delete('/api/rooms/:id/verify-deletion', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const { verificationCode } = req.body;

    if (!verificationCode) {
      return res.status(400).json({ message: 'Verification code is required' });
    }

    // Check if verification code exists and is valid
    const storedData = verificationCodes.get(`room_${id}`);
    if (!storedData) {
      return res.status(400).json({ message: 'No verification request found for this room' });
    }

    // Check if code has expired
    if (Date.now() > storedData.expires) {
      verificationCodes.delete(`room_${id}`);
      return res.status(400).json({ message: 'Verification code has expired' });
    }

    // Check if code matches
    if (storedData.code !== verificationCode.toString()) {
      return res.status(400).json({ message: 'Invalid verification code' });
    }

    // Code is valid, proceed with deletion
    const room = await roomModel.getRoomById(id);
    if (!room) {
      verificationCodes.delete(`room_${id}`);
      return res.status(404).json({ message: 'Room not found' });
    }

    const deletedProductsCount = await productModel.deleteProductsByRoomId(id);
    const success = await roomModel.deleteRoom(id);

    if (success) {
      // Clean up verification code
      verificationCodes.delete(`room_${id}`);

      res.json({
        message: `Room "${room.name}" and its associated ${deletedProductsCount} products deleted successfully.`,
      });
    } else {
      res.status(500).json({ message: 'Failed to delete the room.' });
    }

  } catch (error) {
    console.error('Error verifying and deleting room:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Original delete endpoint (kept for backward compatibility but now requires verification)
app.delete('/api/rooms/:id', async (req, res) => {
  res.status(400).json({
    message: 'Direct room deletion is not allowed. Please use the verification process.',
    requiresVerification: true
  });
});

// Product Routes
app.get('/api/products', async (req, res) => {
  try {
    const { room_id, low_stock } = req.query;
    let products;

    if (low_stock === 'true') {
      products = await productModel.getLowStockProducts();
    } else if (room_id) {
      const roomId = parseInt(room_id);
      products = await productModel.getProductsByRoomId(roomId);
    } else {
      products = await productModel.getAllProducts();
    }
    res.json(products);
  } catch (error) {
    console.error('Error retrieving products:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/products/:item_code', async (req, res) => {
  try {
    const item_code = req.params.item_code;
    const { room_id } = req.query;

    if (room_id) {
      // Get specific product by item_code and room_id
      const product = await productModel.getProductByItemCodeAndRoom(item_code, parseInt(room_id));
      if (product) {
        res.json(product);
      } else {
        res.status(404).json({ message: 'Product not found' });
      }
    } else {
      // Get all products with this item_code (from all rooms)
      const products = await productModel.getProductByItemCode(item_code);
      if (products && products.length > 0) {
        res.json(products);
      } else {
        res.status(404).json({ message: 'Product not found' });
      }
    }
  } catch (error) {
    console.error('Error retrieving product:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/products', async (req, res) => {
  try {
    const {
      item_code,
      room_id,
      item_name,
      car_brand,
      car_model,
      unit_retail_price,
      wholesale_price,
      unit_cost,
      supplier_code,
      available_stock,
      location,
      colour_tape,
      additional_comments,
      product_category,
      low_stock_threshold,
    } = req.body;

    if (
      !item_code ||
      !room_id ||
      !item_name ||
      !car_brand ||
      !car_model ||
      unit_retail_price === undefined ||
      wholesale_price === undefined ||
      unit_cost === undefined ||
      !supplier_code ||
      available_stock === undefined ||
      !location ||
      !colour_tape ||
      !product_category
    ) {
      return res.status(400).json({
        message: 'All fields except additional_comments and low_stock_threshold are required',
      });
    }

    const room = await roomModel.getRoomById(room_id);
    if (!room) {
      return res.status(400).json({ message: 'Invalid room ID' });
    }

    const newProduct = await productModel.createProduct({
      item_code,
      room_id,
      item_name,
      car_brand,
      car_model,
      unit_retail_price,
      wholesale_price,
      unit_cost,
      supplier_code,
      available_stock,
      location,
      colour_tape,
      additional_comments,
      product_category,
      low_stock_threshold: low_stock_threshold || 5,
    });

    res.status(201).json(newProduct);
  } catch (error) {
    console.error('Error creating product:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.put('/api/products/:item_code', async (req, res) => {
  try {
    const item_code = req.params.item_code;
    const { original_room_id } = req.query; // Get original room_id from query params
    const {
      room_id,
      item_name,
      car_brand,
      car_model,
      unit_retail_price,
      wholesale_price,
      unit_cost,
      supplier_code,
      available_stock,
      location,
      colour_tape,
      additional_comments,
      product_category,
      low_stock_threshold,
    } = req.body;

    if (
      !original_room_id ||
      !room_id ||
      !item_name ||
      !car_brand ||
      !car_model ||
      unit_retail_price === undefined ||
      wholesale_price === undefined ||
      unit_cost === undefined ||
      !supplier_code ||
      available_stock === undefined ||
      !location ||
      !colour_tape ||
      !product_category
    ) {
      return res.status(400).json({
        message: 'All fields including original_room_id are required',
      });
    }

    const room = await roomModel.getRoomById(room_id);
    if (!room) {
      return res.status(400).json({ message: 'Invalid room ID' });
    }

    const updatedProduct = await productModel.updateProduct(item_code, parseInt(original_room_id), {
      room_id,
      item_name,
      car_brand,
      car_model,
      unit_retail_price,
      wholesale_price,
      unit_cost,
      supplier_code,
      available_stock,
      location,
      colour_tape,
      additional_comments,
      product_category,
      low_stock_threshold: low_stock_threshold || 5,
    });

    if (updatedProduct) {
      res.json(updatedProduct);
    } else {
      res.status(404).json({ message: 'Product not found' });
    }
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.delete('/api/products/:item_code', async (req, res) => {
  try {
    const item_code = req.params.item_code;
    const { room_id } = req.query; // Get room_id from query params

    if (!room_id) {
      return res.status(400).json({ message: 'room_id is required' });
    }

    const success = await productModel.deleteProduct(item_code, parseInt(room_id));
    if (success) {
      res.json({ message: 'Product deleted successfully' });
    } else {
      res.status(404).json({ message: 'Product not found' });
    }
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// PDF Generation Routes
app.post('/api/generate-pdf', pdfLimiter, async (req, res) => {
  const startTime = new Date();
  console.log(
    `[${startTime.toISOString()}] [START] PDF generation request received.`
  );

  try {
    const {
      date,
      referenceNumber,
      company,
      billing,
      shipping,
      products,
      subtotal,
      tax,
      total,
      paymentMethod,
      comments,
      salespersonName,
    } = req.body;

    if (
      !date ||
      !referenceNumber ||
      !company ||
      !company.name ||
      !company.bankingInformation ||
      !billing ||
      !billing.email ||
      !products ||
      subtotal === undefined ||
      tax === undefined ||
      total === undefined ||
      !paymentMethod
    ) {
      console.log(
        `[${new Date().toISOString()}] [VALIDATION ERROR] Missing required receipt data.`
      );
      return res
        .status(400)
        .json({ message: 'Missing required receipt data.' });
    }

    const recipientEmail = billing.email;
    console.log(
      `[${new Date().toISOString()}] [EMAIL EXTRACTED] Recipient Email: ${recipientEmail}`
    );

    // Process products and calculate profits
    const processedProducts = [];
    let totalProfit = 0;

    for (const product of products) {
      // Use unit cost from frontend if provided, otherwise get from database
      let unitCost = 0;
      if (product.unit_cost !== undefined) {
        // Use unit cost from frontend (for both new and existing products)
        unitCost = parseFloat(product.unit_cost);
      } else if (product.item_code && !product.item_code.startsWith('NEW-')) {
        // Fallback to database for existing products without frontend unit cost
        const productData = await productModel.getProductByItemCode(product.item_code);
        if (productData) {
          unitCost = parseFloat(productData.unit_cost);
        }
      }

      // Use price as selling price (price field is the selling price)
      const sellingPrice = parseFloat(product.unitPriceExcludingTax || product.price);

      const quantity = product.quantity;
      const unitPrice = Math.round(parseFloat(product.unitPriceExcludingTax || product.price));

      // Calculate profit as the difference between selling price and unit cost
      const profitPerUnit = sellingPrice - unitCost;
      // Total profit is the profit per unit multiplied by the quantity
      const totalProductProfit = profitPerUnit * quantity;

      // Add to total profit
      totalProfit += totalProductProfit;

      processedProducts.push({
        name: product.name,
        quantity: quantity,
        unitPriceExcludingTax: unitPrice,
        taxPerProduct: Math.round(parseFloat(product.taxPerProduct || 0.0)),
        totalPrice: Math.round(parseFloat(product.totalPrice || product.price * quantity)),
        item_code: product.item_code || 'N/A',
        is_new: product.item_code && product.item_code.startsWith('NEW-'),
        unit_cost: unitCost,
        profit_per_unit: profitPerUnit,
        total_profit: totalProductProfit,
        room_id: product.room_id,
        room_name: product.room_name
      });
    }

    // Convert logo
    let logoBase64 = '';
    try {
      const logoImage = fs.readFileSync(
        path.join(__dirname, 'public', 'logo.png')
      );
      logoBase64 = Buffer.from(logoImage).toString('base64');
    } catch (err) {
      console.error(
        `[${new Date().toISOString()}] [IMAGE ERROR] Failed to read logo image:`,
        err
      );
    }

    const pdfBuffer = await renderPdf('receipt', {
      date,
      referenceNumber,
      company,
      billing,
      shipping,
      products: processedProducts,
      subtotal: Math.round(parseFloat(subtotal)),
      tax: Math.round(parseFloat(tax)),
      total: Math.round(parseFloat(total)),
      logoBase64,
      paymentMethod: paymentMethod || 'N/A',
      comments: comments || '',
      salespersonName: salespersonName || '',
    });

    // Update product stocks and create sale record
    console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Starting stock deduction for ${processedProducts.length} products`);

    for (const product of processedProducts) {
      console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Processing product: ${product.name} (${product.item_code})`);
      console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Product details: is_new=${product.is_new}, room_id=${product.room_id}, quantity=${product.quantity}`);

      if (!product.is_new && product.room_id) {
        console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Product qualifies for stock deduction - fetching current stock`);

        const currentProduct = await productModel.getProductByItemCodeAndRoom(
          product.item_code,
          product.room_id
        );

        if (currentProduct) {
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Current product found - Current stock: ${currentProduct.available_stock}, Deducting: ${product.quantity}`);

          const newStock = currentProduct.available_stock - product.quantity;
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] New stock will be: ${newStock}`);

          if (newStock < 0) {
            console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION ERROR] Not enough stock! Current: ${currentProduct.available_stock}, Required: ${product.quantity}`);
            return res.status(400).json({
              message: `Not enough stock for product: ${product.item_code} in room: ${product.room_name || 'Unknown'}`,
            });
          }

          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Updating stock for item_code: ${product.item_code}, room_id: ${product.room_id}, new_stock: ${newStock}`);
          await productModel.updateProductStock(product.item_code, product.room_id, newStock);
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Stock updated successfully`);
        } else {
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION ERROR] Product not found in database for item_code: ${product.item_code}, room_id: ${product.room_id}`);
        }
      } else {
        console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Skipping stock deduction - is_new: ${product.is_new}, room_id: ${product.room_id}`);
      }
    }

    // Create sale record
    const saleData = {
      reference_number: referenceNumber,
      date,
      billing_name: billing.name,
      billing_address: billing.address,
      billing_email: billing.email,
      billing_phone: billing.phone || '',
      shipping_name: shipping ? shipping.name : null,
      shipping_address: shipping ? shipping.address : null,
      shipping_email: shipping ? shipping.email : null,
      shipping_phone: shipping ? shipping.phone : null,
      payment_method: paymentMethod,
      subtotal: Math.round(parseFloat(subtotal)),
      tax: Math.round(parseFloat(tax)),
      total: Math.round(parseFloat(total)),
      total_profit: Math.round(totalProfit),
      salesperson_name: salespersonName || '',
      company_name: company.name || 'Shans Accessories PTY LTD',
    };

    const saleId = await salesModel.createSaleRecord(saleData);
    if (!saleId) {
      return res
        .status(500)
        .json({ message: 'Failed to save sale record.' });
    }

    // Create sale items
    for (const product of processedProducts) {
      const saleItemData = {
        sale_id: saleId,
        item_code: product.item_code,
        item_name: product.name,
        quantity: product.quantity,
        unit_price_excluding_tax: product.unitPriceExcludingTax,
        unit_cost: product.unit_cost,
        profit_per_unit: product.profit_per_unit,
        total_profit: product.total_profit,
        tax_per_product: product.taxPerProduct,
        total_price: product.totalPrice,
      };

      // Debug: Log unit cost being saved
      console.log(`[${new Date().toISOString()}] [SALE ITEM DEBUG] Saving item ${product.name} with unit_cost: ${product.unit_cost}`);

      const saleItemSuccess = await salesModel.createSaleItem(saleItemData);
      if (!saleItemSuccess) {
        console.error(
          `[${new Date().toISOString()}] [SALE ITEMS ERROR] Failed to create sale item for product: ${product.item_code}`
        );
        return res
          .status(500)
          .json({ message: 'Failed to save sale items.' });
      }
    }

    // Send email
    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: recipientEmail,
      subject: `Your Receipt from ${company.name}`,
      text:
        'Thank you for your purchase! Please find your receipt attached.',
      attachments: [
        {
          filename: `receipt_${referenceNumber}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf',
        },
      ],
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error(
          `[${new Date().toISOString()}] [EMAIL ERROR]`,
          error
        );
        return res.status(500).json({ message: 'Failed to send email.' });
      }
      console.log(
        `[${new Date().toISOString()}] [EMAIL SENT] ${info.response}`
      );
      res.status(200).json({
        message:
          'PDF generated, stock updated, sale saved, and email sent successfully.',
      });
    });
  } catch (error) {
    console.error(
      `[${new Date().toISOString()}] [PROCESS ERROR]`,
      error
    );
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/generate-quotation', pdfLimiter, async (req, res) => {
  console.log(
    `[${new Date().toISOString()}] [START] Quotation generation request received.`
  );

  try {
    const {
      date,
      referenceNumber,
      billing,
      shipping,
      products,
      subtotal,
      tax,
      total,
      paymentMethod,
      company,
      comments,
      salespersonName,
    } = req.body;

    if (
      !date ||
      !referenceNumber ||
      !billing ||
      !billing.email ||
      !products ||
      subtotal === undefined ||
      tax === undefined ||
      total === undefined ||
      !paymentMethod
    ) {
      return res
        .status(400)
        .json({ message: 'Missing required quotation data.' });
    }

    const companyName = company?.name || 'Shans Accessories PTY LTD';
    const companyBankingInformation = company?.bankingInformation || 'N/A';

    let logoBase64 = '';
    try {
      const logoImage = fs.readFileSync(
        path.join(__dirname, 'public', 'logo.png')
      );
      logoBase64 = Buffer.from(logoImage).toString('base64');
    } catch (err) {
      console.error(
        `[${new Date().toISOString()}] [IMAGE ERROR]`,
        err
      );
    }

    // Log the salesperson name to debug
    console.log(`[${new Date().toISOString()}] [DEBUG] Salesperson Name: ${salespersonName}`);

    const templateData = {
      date,
      referenceNumber,
      billing,
      shipping,
      products: products.map((product) => ({
        item_code: product.item_code || 'N/A',
        name: product.name,
        quantity: product.quantity,
        unitPriceExcludingTax: Math.round(parseFloat(product.unitPriceExcludingTax)),
        taxPerUnit: isNaN(parseFloat(product.taxPerUnit)) ? 0 : Math.round(parseFloat(product.taxPerUnit)),
        totalPrice: Math.round(parseFloat(product.totalPrice)),
      })),
      subtotal: Math.round(parseFloat(subtotal)),
      tax: Math.round(parseFloat(tax)),
      total: Math.round(parseFloat(total)),
      logoBase64,
      paymentMethod: paymentMethod || 'N/A',
      company: {
        name: companyName,
        bankingInformation: companyBankingInformation,
      },
      comments: comments || '',
      salespersonName: salespersonName || '',
    };

    // Log the template data to debug
    console.log(`[${new Date().toISOString()}] [DEBUG] Template Data:`, JSON.stringify(templateData, null, 2));

    const pdfBuffer = await renderPdf('quotation', templateData);

    // Process products for stock deduction
    const processedProducts = [];
    for (const product of products) {
      processedProducts.push({
        name: product.name,
        quantity: product.quantity,
        unitPriceExcludingTax: Math.round(parseFloat(product.unitPriceExcludingTax)),
        taxPerUnit: isNaN(parseFloat(product.taxPerUnit)) ? 0 : Math.round(parseFloat(product.taxPerUnit)),
        totalPrice: Math.round(parseFloat(product.totalPrice)),
        item_code: product.item_code || 'N/A',
        is_new: product.item_code && product.item_code.startsWith('NEW-'),
        room_id: product.room_id,
        room_name: product.room_name
      });
    }

    // Update product stocks for quotation
    console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Starting stock deduction for quotation with ${processedProducts.length} products`);

    for (const product of processedProducts) {
      console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Processing product: ${product.name} (${product.item_code})`);
      console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Product details: is_new=${product.is_new}, room_id=${product.room_id}, quantity=${product.quantity}`);

      if (!product.is_new && product.room_id) {
        console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Product qualifies for stock deduction - fetching current stock`);

        const currentProduct = await productModel.getProductByItemCodeAndRoom(
          product.item_code,
          product.room_id
        );

        if (currentProduct) {
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Current product found - Current stock: ${currentProduct.available_stock}, Deducting: ${product.quantity}`);

          const newStock = currentProduct.available_stock - product.quantity;
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] New stock will be: ${newStock}`);

          if (newStock < 0) {
            console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION ERROR] Not enough stock! Current: ${currentProduct.available_stock}, Required: ${product.quantity}`);
            return res.status(400).json({
              message: `Not enough stock for product: ${product.item_code} in room: ${product.room_name || 'Unknown'}`,
            });
          }

          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Updating stock for item_code: ${product.item_code}, room_id: ${product.room_id}, new_stock: ${newStock}`);
          await productModel.updateProductStock(product.item_code, product.room_id, newStock);
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Stock updated successfully`);
        } else {
          console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION ERROR] Product not found in database for item_code: ${product.item_code}, room_id: ${product.room_id}`);
        }
      } else {
        console.log(`[${new Date().toISOString()}] [STOCK DEDUCTION] Skipping stock deduction - is_new: ${product.is_new}, room_id: ${product.room_id}`);
      }
    }

    // Save quotation to database
    try {
      const quotationData = {
        reference_number: referenceNumber,
        date: date,
        billing_name: billing.name || '',
        billing_address: billing.address || '',
        billing_email: billing.email,
        billing_phone: billing.phone || '',
        shipping_name: shipping?.name || '',
        shipping_address: shipping?.address || '',
        shipping_email: shipping?.email || '',
        shipping_phone: shipping?.phone || '',
        payment_method: paymentMethod,
        subtotal: parseFloat(subtotal),
        tax: parseFloat(tax),
        total: parseFloat(total),
        salesperson_name: salespersonName || '',
        company_name: companyName,
        comments: comments || '',
        products: products
      };

      await quotationModel.createQuotation(quotationData);
      console.log(`[${new Date().toISOString()}] [DATABASE] Quotation saved to database with reference: ${referenceNumber}`);
    } catch (dbError) {
      console.error(`[${new Date().toISOString()}] [DATABASE ERROR] Failed to save quotation:`, dbError);
      // Continue with email sending even if database save fails
    }

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: billing.email,
      subject: `Your Quotation from ${companyName}`,
      text: `Dear ${billing.name},\n\nPlease find your quotation attached.\n\nReference Number: ${referenceNumber}\n\nBest regards,\nShans Accessories PTY LTD`,
      attachments: [
        {
          filename: `Quotation_${referenceNumber}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf',
        },
      ],
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error(
          `[${new Date().toISOString()}] [EMAIL ERROR]`,
          error
        );
        return res.status(500).json({ message: 'Failed to send email.' });
      }
      console.log(
        `[${new Date().toISOString()}] [EMAIL SENT] ${info.response}`
      );
      res.status(200).json({
        message: 'Quotation PDF generated and email sent successfully.',
      });
    });
  } catch (error) {
    console.error(
      `[${new Date().toISOString()}] [PROCESS ERROR]`,
      error
    );
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/generate-invoice', pdfLimiter, async (req, res) => {
  console.log(
    `[${new Date().toISOString()}] [START] Invoice generation request received.`
  );

  try {
    const {
      date,
      referenceNumber,
      billing,
      shipping,
      products,
      subtotal,
      tax,
      total,
      paymentMethod,
      company,
      comments,
      salespersonName,
    } = req.body;

    if (
      !date ||
      !referenceNumber ||
      !billing ||
      !billing.email ||
      !products ||
      subtotal === undefined ||
      tax === undefined ||
      total === undefined ||
      !paymentMethod
    ) {
      return res
        .status(400)
        .json({ message: 'Missing required invoice data.' });
    }

    const companyName = company?.name || 'Shans Accessories PTY LTD';
    const companyBankingInformation = company?.bankingInformation || 'N/A';

    let logoBase64 = '';
    try {
      const logoImage = fs.readFileSync(
        path.join(__dirname, 'public', 'logo.png')
      );
      logoBase64 = Buffer.from(logoImage).toString('base64');
    } catch (err) {
      console.error(
        `[${new Date().toISOString()}] [IMAGE ERROR]`,
        err
      );
    }

    const templateData = {
      date,
      referenceNumber,
      billing,
      shipping,
      products: products.map((product) => ({
        item_code: product.item_code || 'N/A',
        name: product.name,
        quantity: product.quantity,
        unitPriceExcludingTax: Math.round(parseFloat(product.unitPriceExcludingTax)),
        taxPerUnit: isNaN(parseFloat(product.taxPerUnit)) ? 0 : Math.round(parseFloat(product.taxPerUnit)),
        totalPrice: Math.round(parseFloat(product.totalPrice)),
      })),
      subtotal: Math.round(parseFloat(subtotal)),
      tax: Math.round(parseFloat(tax)),
      total: Math.round(parseFloat(total)),
      logoBase64,
      paymentMethod: paymentMethod || 'N/A',
      company: {
        name: companyName,
        bankingInformation: companyBankingInformation,
      },
      comments: comments || '',
      salespersonName: salespersonName || '',
    };

    const pdfBuffer = await renderPdf('invoice', templateData);

    // Save invoice to database
    try {
      const invoiceData = {
        reference_number: referenceNumber,
        date: date,
        billing_name: billing.name || '',
        billing_address: billing.address || '',
        billing_email: billing.email,
        billing_phone: billing.phone || '',
        shipping_name: shipping?.name || '',
        shipping_address: shipping?.address || '',
        shipping_email: shipping?.email || '',
        shipping_phone: shipping?.phone || '',
        payment_method: paymentMethod,
        subtotal: parseFloat(subtotal),
        tax: parseFloat(tax),
        total: parseFloat(total),
        salesperson_name: salespersonName || '',
        company_name: companyName,
        comments: comments || '',
        products: products
      };

      await invoiceModel.createInvoice(invoiceData);
      console.log(`[${new Date().toISOString()}] [DATABASE] Invoice saved to database with reference: ${referenceNumber}`);
    } catch (dbError) {
      console.error(`[${new Date().toISOString()}] [DATABASE ERROR] Failed to save invoice:`, dbError);
      // Continue with email sending even if database save fails
    }

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: billing.email,
      subject: `Your Invoice from ${companyName}`,
      text: `Dear ${billing.name},\n\nPlease find your invoice attached.\n\nReference Number: ${referenceNumber}\n\nBest regards,\nShans Accessories PTY LTD`,
      attachments: [
        {
          filename: `Invoice_${referenceNumber}.pdf`,
          content: pdfBuffer,
          contentType: 'application/pdf',
        },
      ],
    };

    transporter.sendMail(mailOptions, (error, info) => {
      if (error) {
        console.error(
          `[${new Date().toISOString()}] [EMAIL ERROR]`,
          error
        );
        return res.status(500).json({ message: 'Failed to send email.' });
      }
      console.log(
        `[${new Date().toISOString()}] [EMAIL SENT] ${info.response}`
      );
      res.status(200).json({
        message: 'Invoice PDF generated and email sent successfully.',
      });
    });
  } catch (error) {
    console.error(
      `[${new Date().toISOString()}] [PROCESS ERROR]`,
      error
    );
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Car Model Routes
app.get('/api/car-models', async (req, res) => {
  try {
    const models = await carModelModel.getAllModels();
    res.json(models);
  } catch (error) {
    console.error('Error retrieving car models:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/car-models/brand/:brand_id', async (req, res) => {
  try {
    const brand_id = req.params.brand_id;
    const models = await carModelModel.getModelsByBrand(brand_id);
    res.json(models);
  } catch (error) {
    console.error('Error retrieving car models by brand:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/car-models/:model_id', async (req, res) => {
  try {
    const model_id = req.params.model_id;
    const model = await carModelModel.getModelById(model_id);
    if (model) {
      res.json(model);
    } else {
      res.status(404).json({ message: 'Car model not found' });
    }
  } catch (error) {
    console.error('Error retrieving car model:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/car-models', async (req, res) => {
  try {
    const { model_id, name, brand_id } = req.body;
    if (!model_id || !name || !brand_id) {
      return res.status(400).json({ message: 'Model ID, name, and brand ID are required' });
    }

    // Check if model already exists
    const existingModel = await carModelModel.getModelById(model_id);
    if (existingModel) {
      return res.status(400).json({ message: 'Car model with this ID already exists' });
    }

    // Check if brand exists
    const brand = await carBrandModel.getBrandById(brand_id);
    if (!brand) {
      return res.status(400).json({ message: 'Car brand not found' });
    }

    const newModel = await carModelModel.createModel({ model_id, name, brand_id });
    res.status(201).json(newModel);
  } catch (error) {
    console.error('Error creating car model:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.put('/api/car-models/:model_id', async (req, res) => {
  try {
    const model_id = req.params.model_id;
    const { name, brand_id } = req.body;
    if (!name || !brand_id) {
      return res.status(400).json({ message: 'Model name and brand ID are required' });
    }

    // Check if brand exists
    const brand = await carBrandModel.getBrandById(brand_id);
    if (!brand) {
      return res.status(400).json({ message: 'Car brand not found' });
    }

    const updatedModel = await carModelModel.updateModel(model_id, name, brand_id);
    if (updatedModel) {
      res.json(updatedModel);
    } else {
      res.status(404).json({ message: 'Car model not found' });
    }
  } catch (error) {
    console.error('Error updating car model:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.delete('/api/car-models/:model_id', async (req, res) => {
  try {
    const model_id = req.params.model_id;

    // Check if model is in use
    const isInUse = await carModelModel.isModelInUse(model_id);
    if (isInUse) {
      return res.status(400).json({ message: 'Cannot delete car model as it is being used by products' });
    }

    const success = await carModelModel.deleteModel(model_id);
    if (success) {
      res.json({ message: 'Car model deleted successfully' });
    } else {
      res.status(404).json({ message: 'Car model not found' });
    }
  } catch (error) {
    console.error('Error deleting car model:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Car Brand Routes
app.get('/api/car-brands', async (req, res) => {
  try {
    const brands = await carBrandModel.getAllBrands();
    res.json(brands);
  } catch (error) {
    console.error('Error retrieving car brands:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/car-brands/:brand_id', async (req, res) => {
  try {
    const brand_id = req.params.brand_id;
    const brand = await carBrandModel.getBrandById(brand_id);
    if (brand) {
      res.json(brand);
    } else {
      res.status(404).json({ message: 'Car brand not found' });
    }
  } catch (error) {
    console.error('Error retrieving car brand:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/car-brands', async (req, res) => {
  try {
    const { brand_id, name } = req.body;
    if (!brand_id || !name) {
      return res.status(400).json({ message: 'Brand ID and name are required' });
    }

    // Check if brand already exists
    const existingBrand = await carBrandModel.getBrandById(brand_id);
    if (existingBrand) {
      return res.status(400).json({ message: 'Car brand with this ID already exists' });
    }

    const newBrand = await carBrandModel.createBrand({ brand_id, name });
    res.status(201).json(newBrand);
  } catch (error) {
    console.error('Error creating car brand:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.put('/api/car-brands/:brand_id', async (req, res) => {
  try {
    const brand_id = req.params.brand_id;
    const { name } = req.body;
    if (!name) {
      return res.status(400).json({ message: 'Brand name is required' });
    }

    const updatedBrand = await carBrandModel.updateBrand(brand_id, name);
    if (updatedBrand) {
      res.json(updatedBrand);
    } else {
      res.status(404).json({ message: 'Car brand not found' });
    }
  } catch (error) {
    console.error('Error updating car brand:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.delete('/api/car-brands/:brand_id', async (req, res) => {
  try {
    const brand_id = req.params.brand_id;

    // Check if brand is in use
    const isInUse = await carBrandModel.isBrandInUse(brand_id);
    if (isInUse) {
      return res.status(400).json({ message: 'Cannot delete car brand as it is being used by products' });
    }

    const success = await carBrandModel.deleteBrand(brand_id);
    if (success) {
      res.json({ message: 'Car brand deleted successfully' });
    } else {
      res.status(404).json({ message: 'Car brand not found' });
    }
  } catch (error) {
    console.error('Error deleting car brand:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Category Routes
app.get('/api/categories', async (req, res) => {
  try {
    const categories = await categoryModel.getAllCategories();
    res.json(categories);
  } catch (error) {
    console.error('Error retrieving categories:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/categories/:category_id', async (req, res) => {
  try {
    const category_id = req.params.category_id;
    const category = await categoryModel.getCategoryById(category_id);
    if (category) {
      res.json(category);
    } else {
      res.status(404).json({ message: 'Category not found' });
    }
  } catch (error) {
    console.error('Error retrieving category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/categories', async (req, res) => {
  try {
    const { category_id, name } = req.body;
    if (!category_id || !name) {
      return res.status(400).json({ message: 'Category ID and name are required' });
    }

    // Check if category already exists
    const existingCategory = await categoryModel.getCategoryById(category_id);
    if (existingCategory) {
      return res.status(400).json({ message: 'Category with this ID already exists' });
    }

    const newCategory = await categoryModel.createCategory({ category_id, name });
    res.status(201).json(newCategory);
  } catch (error) {
    console.error('Error creating category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.put('/api/categories/:category_id', async (req, res) => {
  try {
    const category_id = req.params.category_id;
    const { name } = req.body;
    if (!name) {
      return res.status(400).json({ message: 'Category name is required' });
    }

    const updatedCategory = await categoryModel.updateCategory(category_id, name);
    if (updatedCategory) {
      res.json(updatedCategory);
    } else {
      res.status(404).json({ message: 'Category not found' });
    }
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.delete('/api/categories/:category_id', async (req, res) => {
  try {
    const category_id = req.params.category_id;

    // Check if category is in use
    const isInUse = await categoryModel.isCategoryInUse(category_id);
    if (isInUse) {
      return res.status(400).json({ message: 'Cannot delete category as it is being used by products' });
    }

    const success = await categoryModel.deleteCategory(category_id);
    if (success) {
      res.json({ message: 'Category deleted successfully' });
    } else {
      res.status(404).json({ message: 'Category not found' });
    }
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Sales Routes
app.get('/api/sales', async (req, res) => {
  try {
    const sales = await salesModel.getAllSales();
    res.json(sales);
  } catch (error) {
    console.error('Error fetching sales:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});


// -------------------- Expense Routes -------------------- //

// Expense Category Routes
app.get('/api/expense-categories', async (req, res) => {
  try {
    const categories = await expenseCategoryModel.getAllCategories();
    res.json(categories);
  } catch (error) {
    console.error('Error retrieving expense categories:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/expense-categories/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const category = await expenseCategoryModel.getCategoryById(id);
    if (category) {
      res.json(category);
    } else {
      res.status(404).json({ message: 'Expense category not found' });
    }
  } catch (error) {
    console.error('Error retrieving expense category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/expense-categories', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { name, description, color } = req.body;
    if (!name) {
      return res.status(400).json({ message: 'Category name is required' });
    }

    const newCategory = await expenseCategoryModel.createCategory({ name, description, color });
    res.status(201).json(newCategory);
  } catch (error) {
    console.error('Error creating expense category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.put('/api/expense-categories/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const { name, description, color } = req.body;
    if (!name) {
      return res.status(400).json({ message: 'Category name is required' });
    }

    const updatedCategory = await expenseCategoryModel.updateCategory(id, { name, description, color });
    if (updatedCategory) {
      res.json(updatedCategory);
    } else {
      res.status(404).json({ message: 'Expense category not found' });
    }
  } catch (error) {
    console.error('Error updating expense category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.delete('/api/expense-categories/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    // Check if category is in use
    const isInUse = await expenseCategoryModel.isCategoryInUse(id);
    if (isInUse) {
      return res.status(400).json({ message: 'Cannot delete expense category as it is being used by expenses' });
    }

    const success = await expenseCategoryModel.deleteCategory(id);
    if (success) {
      res.json({ message: 'Expense category deleted successfully' });
    } else {
      res.status(404).json({ message: 'Expense category not found' });
    }
  } catch (error) {
    console.error('Error deleting expense category:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Expense Routes


app.get('/api/expenses/totals', async (req, res) => {
  try {
    const { year, month } = req.query;

    // Pass year and month to respect month isolation
    const totals = await expenseModel.getTotalExpensesByCategory(
      year ? parseInt(year) : null,
      month ? parseInt(month) : null
    );
    const grandTotal = await expenseModel.getTotalExpenses(
      year ? parseInt(year) : null,
      month ? parseInt(month) : null
    );

    res.json({ categories: totals, grandTotal });
  } catch (error) {
    console.error('Error retrieving expense totals:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Get recurring expenses (templates) - must come before /api/expenses/:id
app.get('/api/expenses/recurring', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const recurringExpenses = await expenseModel.getRecurringExpenses();
    res.json(recurringExpenses);
  } catch (error) {
    console.error('Error retrieving recurring expenses:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/expenses', async (req, res) => {
  try {
    const { expense_type, year, month } = req.query;
    let expenses;

    if (year && month) {
      // Get expenses for specific month
      expenses = await expenseModel.getExpensesByMonth(
        parseInt(year),
        parseInt(month),
        null, // category_id no longer used
        expense_type
      );
    } else if (expense_type) {
      expenses = await expenseModel.getExpensesByType(expense_type);
    } else {
      expenses = await expenseModel.getAllExpenses();
    }

    res.json(expenses);
  } catch (error) {
    console.error('Error retrieving expenses:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.get('/api/expenses/:id', async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    // Validate that id is a valid number
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid expense ID' });
    }

    const expense = await expenseModel.getExpenseById(id);
    if (expense) {
      res.json(expense);
    } else {
      res.status(404).json({ message: 'Expense not found' });
    }
  } catch (error) {
    console.error('Error retrieving expense:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.post('/api/expenses', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { category_id, name, amount, description, expense_date, expense_type, is_recurring, media_url } = req.body;

    if (!category_id || !name || amount === undefined || !expense_date) {
      return res.status(400).json({
        message: 'Category ID, name, amount, and expense date are required'
      });
    }

    // Validate expense_type
    if (expense_type && !['fixed', 'variable'].includes(expense_type)) {
      return res.status(400).json({
        message: 'Expense type must be either "fixed" or "variable"'
      });
    }

    // Validate category exists
    const category = await expenseCategoryModel.getCategoryById(category_id);
    if (!category) {
      return res.status(400).json({ message: 'Invalid category ID' });
    }

    const newExpense = await expenseModel.createExpense({
      category_id,
      name,
      amount: parseFloat(amount),
      description,
      expense_date,
      expense_type: expense_type || 'variable',
      is_recurring: is_recurring || false,
      recurring_template_id: null,
      media_url: media_url || null
    });

    res.status(201).json(newExpense);
  } catch (error) {
    console.error('Error creating expense:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.put('/api/expenses/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);

    // Validate that id is a valid number
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid expense ID' });
    }

    const { category_id, name, amount, description, expense_date, expense_type, is_recurring, media_url } = req.body;

    if (!category_id || !name || amount === undefined || !expense_date) {
      return res.status(400).json({
        message: 'Category ID, name, amount, and expense date are required'
      });
    }

    // Validate expense_type
    if (expense_type && !['fixed', 'variable'].includes(expense_type)) {
      return res.status(400).json({
        message: 'Expense type must be either "fixed" or "variable"'
      });
    }

    // Validate category exists
    const category = await expenseCategoryModel.getCategoryById(category_id);
    if (!category) {
      return res.status(400).json({ message: 'Invalid category ID' });
    }

    // Get the current expense to preserve recurring_template_id
    const currentExpense = await expenseModel.getExpenseById(id);
    if (!currentExpense) {
      return res.status(404).json({ message: 'Expense not found' });
    }

    const updatedExpense = await expenseModel.updateExpense(id, {
      category_id,
      name,
      amount: parseFloat(amount),
      description,
      expense_date,
      expense_type: expense_type || currentExpense.expense_type || 'variable',
      is_recurring: is_recurring || false,
      recurring_template_id: currentExpense.recurring_template_id,
      media_url: media_url !== undefined ? media_url : currentExpense.media_url
    });

    if (updatedExpense) {
      res.json(updatedExpense);
    } else {
      res.status(404).json({ message: 'Expense not found' });
    }
  } catch (error) {
    console.error('Error updating expense:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

app.delete('/api/expenses/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const id = parseInt(req.params.id);
    const { year, month } = req.query; // Get year and month from query params for month isolation

    // Validate that id is a valid number
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid expense ID' });
    }

    let success;

    // If year and month are provided, use month-isolated deletion
    if (year && month) {
      success = await expenseModel.deleteExpenseWithIsolation(
        id,
        parseInt(year),
        parseInt(month)
      );
    } else {
      // Use regular deletion for global operations
      success = await expenseModel.deleteExpense(id);
    }

    if (success) {
      const message = (year && month)
        ? `Expense removed from ${year}-${month} (will remain in other months)`
        : 'Expense deleted successfully';
      res.json({ message });
    } else {
      res.status(404).json({ message: 'Expense not found' });
    }
  } catch (error) {
    console.error('Error deleting expense:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});



// Generate recurring expenses for a specific month
app.post('/api/expenses/recurring/generate', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { year, month } = req.body;

    if (!year || !month) {
      return res.status(400).json({ message: 'Year and month are required' });
    }

    const createdExpenses = await expenseModel.createRecurringExpensesForMonth(
      parseInt(year),
      parseInt(month)
    );

    res.json({
      message: `Generated ${createdExpenses.length} recurring expenses for ${year}-${month}`,
      expenses: createdExpenses
    });
  } catch (error) {
    console.error('Error generating recurring expenses:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Restore a recurring expense to a specific month (remove exclusion)
app.post('/api/expenses/recurring/restore', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { recurringTemplateId, year, month } = req.body;

    if (!recurringTemplateId || !year || !month) {
      return res.status(400).json({ message: 'Recurring template ID, year, and month are required' });
    }

    const success = await expenseModel.removeRecurringExpenseExclusion(
      parseInt(recurringTemplateId),
      parseInt(year),
      parseInt(month)
    );

    if (success) {
      res.json({
        message: `Recurring expense restored to ${year}-${month}`
      });
    } else {
      res.status(404).json({ message: 'No exclusion found for this expense and month' });
    }
  } catch (error) {
    console.error('Error restoring recurring expense:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// -------------------- Quotation and Invoice Conversion Routes -------------------- //

// Get all quotations
app.get('/api/quotations', async (req, res) => {
  try {
    const quotations = await quotationModel.getAllQuotations();
    res.json(quotations);
  } catch (error) {
    console.error('Error retrieving quotations:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Get single quotation by ID
app.get('/api/quotations/:id', async (req, res) => {
  try {
    const quotationId = req.params.id; // Keep as string to handle large IDs
    const quotation = await quotationModel.getQuotationById(quotationId);
    if (quotation) {
      res.json(quotation);
    } else {
      res.status(404).json({ message: 'Quotation not found' });
    }
  } catch (error) {
    console.error('Error retrieving quotation:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Get all invoices
app.get('/api/invoices', async (req, res) => {
  try {
    const invoices = await invoiceModel.getAllInvoices();
    res.json(invoices);
  } catch (error) {
    console.error('Error retrieving invoices:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Get single invoice by ID
app.get('/api/invoices/:id', async (req, res) => {
  try {
    const invoiceId = req.params.id; // Keep as string to handle large IDs
    const invoice = await invoiceModel.getInvoiceById(invoiceId);
    if (invoice) {
      res.json(invoice);
    } else {
      res.status(404).json({ message: 'Invoice not found' });
    }
  } catch (error) {
    console.error('Error retrieving invoice:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Convert quotation to receipt
app.post('/api/convert-quotation-to-receipt/:id', async (req, res) => {
  try {
    const quotationId = req.params.id; // Keep as string to handle large IDs
    const { customDate } = req.body; // Accept custom date from request body

    // Get quotation data
    const quotation = await quotationModel.getQuotationById(quotationId);
    if (!quotation) {
      return res.status(404).json({ message: 'Quotation not found' });
    }

    // Prepare receipt data from quotation
    const receiptData = {
      date: customDate || new Date().toISOString().slice(0, 10), // Use custom date if provided
      referenceNumber: 'RCP-' + Date.now(), // Generate new reference for receipt
      company: {
        name: quotation.company_name,
        bankingInformation: 'Standard Bank\nAccount: *********\nBranch: 051001'
      },
      billing: {
        name: quotation.billing_name,
        address: quotation.billing_address,
        email: quotation.billing_email,
        phone: quotation.billing_phone
      },
      shipping: quotation.shipping_name ? {
        name: quotation.shipping_name,
        address: quotation.shipping_address,
        email: quotation.shipping_email,
        phone: quotation.shipping_phone
      } : null,
      paymentMethod: quotation.payment_method,
      comments: quotation.comments,
      salespersonName: quotation.salesperson_name,
      products: quotation.items.map(item => ({
        item_code: item.item_code,
        name: item.item_name,
        quantity: item.quantity,
        unitPriceExcludingTax: item.unit_price_excluding_tax,
        taxPerUnit: item.tax_per_product,
        totalPrice: item.total_price,
        unit_cost: 0, // Default since we don't have cost in quotation
        room_id: item.room_id // Include room_id for stock deduction
      })),
      subtotal: quotation.subtotal,
      tax: quotation.tax,
      total: quotation.total
    };

    // Call the existing receipt generation endpoint
    const response = await fetch('http://localhost:8000/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(receiptData)
    });

    if (response.ok) {
      // Delete the quotation after successful receipt generation
      await quotationModel.deleteQuotation(quotationId);
      res.json({ message: 'Receipt generated successfully and quotation removed' });
    } else {
      const errorData = await response.json();
      res.status(500).json({ message: 'Failed to generate receipt', error: errorData.message });
    }
  } catch (error) {
    console.error('Error converting quotation to receipt:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Convert invoice to receipt
app.post('/api/convert-invoice-to-receipt/:id', async (req, res) => {
  try {
    const invoiceId = parseInt(req.params.id);
    const { customDate } = req.body; // Accept custom date from request body

    // Get invoice data
    const invoice = await invoiceModel.getInvoiceById(invoiceId);
    if (!invoice) {
      return res.status(404).json({ message: 'Invoice not found' });
    }

    // Prepare receipt data from invoice
    const receiptData = {
      date: customDate || new Date().toISOString().slice(0, 10), // Use custom date if provided
      referenceNumber: 'RCP-' + Date.now(), // Generate new reference for receipt
      company: {
        name: invoice.company_name,
        bankingInformation: 'Standard Bank\nAccount: *********\nBranch: 051001'
      },
      billing: {
        name: invoice.billing_name,
        address: invoice.billing_address,
        email: invoice.billing_email,
        phone: invoice.billing_phone
      },
      shipping: invoice.shipping_name ? {
        name: invoice.shipping_name,
        address: invoice.shipping_address,
        email: invoice.shipping_email,
        phone: invoice.shipping_phone
      } : null,
      paymentMethod: invoice.payment_method,
      comments: invoice.comments,
      salespersonName: invoice.salesperson_name,
      products: invoice.items.map(item => ({
        item_code: item.item_code,
        name: item.item_name,
        quantity: item.quantity,
        unitPriceExcludingTax: item.unit_price_excluding_tax,
        taxPerUnit: item.tax_per_product,
        totalPrice: item.total_price,
        unit_cost: 0, // Default since we don't have cost in invoice
        room_id: item.room_id // Include room_id for stock deduction
      })),
      subtotal: invoice.subtotal,
      tax: invoice.tax,
      total: invoice.total
    };

    // Call the existing receipt generation endpoint
    const response = await fetch('http://localhost:8000/api/generate-pdf', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(receiptData)
    });

    if (response.ok) {
      // Delete the invoice after successful receipt generation
      await invoiceModel.deleteInvoice(invoiceId);
      res.json({ message: 'Receipt generated successfully and invoice removed' });
    } else {
      const errorData = await response.json();
      res.status(500).json({ message: 'Failed to generate receipt', error: errorData.message });
    }
  } catch (error) {
    console.error('Error converting invoice to receipt:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Delete quotation
app.delete('/api/quotations/:id', async (req, res) => {
  try {
    const quotationId = req.params.id; // Keep as string to handle large IDs
    const success = await quotationModel.deleteQuotation(quotationId);
    if (success) {
      res.json({ message: 'Quotation deleted successfully' });
    } else {
      res.status(404).json({ message: 'Quotation not found' });
    }
  } catch (error) {
    console.error('Error deleting quotation:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Update quotation
app.put('/api/quotations/:id', async (req, res) => {
  try {
    const quotationId = req.params.id; // Keep as string to handle large IDs
    const quotationData = req.body;

    // Validate required fields
    if (!quotationData.reference_number || !quotationData.date || !quotationData.billing_name ||
        !quotationData.billing_email || !quotationData.payment_method) {
      return res.status(400).json({ message: 'Missing required quotation data' });
    }

    const updatedId = await quotationModel.updateQuotation(quotationId, quotationData);
    res.json({ message: 'Quotation updated successfully', id: updatedId });
  } catch (error) {
    console.error('Error updating quotation:', error);
    if (error.message === 'Quotation not found') {
      res.status(404).json({ message: 'Quotation not found' });
    } else {
      res.status(500).json({ message: 'Internal Server Error' });
    }
  }
});

// Delete invoice
app.delete('/api/invoices/:id', async (req, res) => {
  try {
    const invoiceId = parseInt(req.params.id);
    const success = await invoiceModel.deleteInvoice(invoiceId);
    if (success) {
      res.json({ message: 'Invoice deleted successfully' });
    } else {
      res.status(404).json({ message: 'Invoice not found' });
    }
  } catch (error) {
    console.error('Error deleting invoice:', error);
    res.status(500).json({ message: 'Internal Server Error' });
  }
});

// Update invoice
app.put('/api/invoices/:id', async (req, res) => {
  try {
    const invoiceId = parseInt(req.params.id);
    const invoiceData = req.body;

    // Validate required fields
    if (!invoiceData.reference_number || !invoiceData.date || !invoiceData.billing_name ||
        !invoiceData.billing_email || !invoiceData.payment_method) {
      return res.status(400).json({ message: 'Missing required invoice data' });
    }

    const updatedId = await invoiceModel.updateInvoice(invoiceId, invoiceData);
    res.json({ message: 'Invoice updated successfully', id: updatedId });
  } catch (error) {
    console.error('Error updating invoice:', error);
    if (error.message === 'Invoice not found') {
      res.status(404).json({ message: 'Invoice not found' });
    } else {
      res.status(500).json({ message: 'Internal Server Error' });
    }
  }
});

// -------------------- Backup Routes -------------------- //
app.use('/api/backup', backupRoutes);

// -------------------- Expense Media Routes -------------------- //
app.use('/api/expense-media', expenseMediaRoutes);





// Health check endpoints
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Fix database sequences endpoint
app.post('/api/fix-sequences', async (req, res) => {
  try {
    const { fixSequences } = require('./database');
    await fixSequences();
    res.json({
      success: true,
      message: 'Database sequences fixed successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fixing sequences via API:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fix database sequences',
      error: error.message
    });
  }
});

// Migration endpoint to add media_url column (temporary - remove after migration)
app.get('/api/migrate/media-url', async (req, res) => {
  let client;

  try {
    client = await pool.connect();

    // Check if media_url column exists
    const columnCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'expenses'
      AND column_name = 'media_url'
    `);

    const results = [];

    if (columnCheck.rows.length === 0) {
      // Column doesn't exist, so add it
      await client.query(`ALTER TABLE expenses ADD COLUMN media_url TEXT`);
      results.push('Added media_url column');
    } else {
      results.push('media_url column already exists');
    }

    // Verify column was added
    const verifyCheck = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'expenses'
      AND column_name = 'media_url'
    `);

    res.json({
      success: true,
      message: 'Migration completed',
      results: results,
      columns: verifyCheck.rows
    });

  } catch (error) {
    console.error('Migration error:', error);
    res.status(500).json({
      success: false,
      message: 'Migration failed',
      error: error.message
    });
  } finally {
    if (client) {
      client.release();
    }
  }
});

// Migration endpoint to add recurring columns (temporary - remove after migration)
app.get('/api/migrate/recurring', async (req, res) => {
  let client;

  try {
    client = await pool.connect();

    // Check if columns exist
    const columnCheck = await client.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'expenses'
      AND column_name IN ('is_recurring', 'recurring_template_id')
    `);

    const existingColumns = columnCheck.rows.map(row => row.column_name);
    const results = [];

    // Add is_recurring column if it doesn't exist
    if (!existingColumns.includes('is_recurring')) {
      await client.query(`ALTER TABLE expenses ADD COLUMN is_recurring BOOLEAN DEFAULT FALSE`);
      results.push('Added is_recurring column');
    } else {
      results.push('is_recurring column already exists');
    }

    // Add recurring_template_id column if it doesn't exist
    if (!existingColumns.includes('recurring_template_id')) {
      await client.query(`ALTER TABLE expenses ADD COLUMN recurring_template_id INTEGER`);
      results.push('Added recurring_template_id column');

      // Add foreign key constraint
      try {
        await client.query(`
          ALTER TABLE expenses
          ADD CONSTRAINT fk_recurring_template
          FOREIGN KEY (recurring_template_id) REFERENCES expenses(id) ON DELETE SET NULL
        `);
        results.push('Added foreign key constraint');
      } catch (fkError) {
        results.push('Foreign key constraint may already exist');
      }
    } else {
      results.push('recurring_template_id column already exists');
    }

    // Verify columns were added
    const verifyCheck = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'expenses'
      AND column_name IN ('is_recurring', 'recurring_template_id')
      ORDER BY column_name
    `);

    res.json({
      success: true,
      message: 'Migration completed',
      results: results,
      columns: verifyCheck.rows
    });

  } catch (error) {
    console.error('Migration error:', error);
    res.status(500).json({
      success: false,
      message: 'Migration failed',
      error: error.message
    });
  } finally {
    if (client) {
      client.release();
    }
  }
});

// -------------------- Start the Server -------------------- //
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

module.exports = app;
