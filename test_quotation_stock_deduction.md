# Test Plan: Quotation Stock Deduction Fix

## Issue Fixed
The quotation generation endpoint (`/api/generate-quotation`) was not deducting stock from the database when quotations were created. This has been fixed by adding the same stock deduction logic that exists in the receipt generation endpoint.

## Changes Made
1. **Added stock deduction logic to `/api/generate-quotation` endpoint** in `backend/index.js` (lines 1147-1230)
   - Process products to identify existing vs new products
   - Check stock availability before deduction
   - Update product stock in database for existing products
   - Skip stock deduction for new products (item codes starting with "NEW-")
   - Return error if insufficient stock

## Testing Steps

### 1. Test with Existing Products (Should Deduct Stock)
1. Create a quotation with existing products that have room_id
2. Verify stock is deducted from the database
3. Check that the quotation is saved successfully
4. Verify email is sent

### 2. Test with New Products (Should Skip Stock Deduction)
1. Create a quotation with new products (item_code starts with "NEW-")
2. Verify no stock deduction occurs
3. Check that the quotation is saved successfully

### 3. Test with Insufficient Stock
1. Try to create a quotation with quantity greater than available stock
2. Verify the request fails with appropriate error message
3. Verify no quotation is created
4. Verify no email is sent

### 4. Test Mixed Products
1. Create a quotation with both existing and new products
2. Verify stock is only deducted for existing products
3. Verify quotation is saved successfully

## Expected Behavior
- ✅ Stock deduction now works for quotations (same as receipts)
- ✅ Existing products with room_id have stock deducted
- ✅ New products (item_code starts with "NEW-") skip stock deduction
- ✅ Insufficient stock returns error and prevents quotation creation
- ✅ Quotation is saved to database after successful stock deduction
- ✅ Email is sent after successful processing

## Code Changes Summary
The fix adds comprehensive stock deduction logic to the quotation generation endpoint, making it consistent with the receipt generation functionality. The logic includes:
- Product processing and validation
- Stock availability checking
- Database stock updates
- Error handling for insufficient stock
- Detailed logging for debugging
